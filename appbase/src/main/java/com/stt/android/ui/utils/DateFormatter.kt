package com.stt.android.ui.utils

import android.content.Context
import android.content.res.Resources
import com.stt.android.R
import dagger.hilt.android.qualifiers.ApplicationContext
import java.time.LocalDate
import java.time.Month
import java.time.Year
import javax.inject.Inject

class DateFormatter @Inject constructor(
    @ApplicationContext appContext: Context,
) {
    private val resources: Resources = appContext.resources

    fun formatRelativeDate(date: LocalDate): String {
        val today = LocalDate.now()
        return when {
            date == today -> resources.getString(R.string.today)
            date == today.minusDays(1L) -> resources.getString(R.string.yesterday)
            date.year == today.year -> getAbbreviatedMonthDate(date)
            else -> getYearAbbreviatedMonthDate(date)
        }
    }

    fun formatDate(date: LocalDate): String {
        val today = LocalDate.now()
        return when {
            date.year == today.year -> getAbbreviatedMonthDate(date)
            else -> getYearAbbreviatedMonthDate(date)
        }
    }

    fun formatRelativeMonth(date: LocalDate): String {
        val today = LocalDate.now()
        return when {
            date.sameMonthAs(today) -> resources.getString(R.string.this_month)
            date.lastMonthOf(today) -> resources.getString(R.string.last_month)
            date.sameYearAs(today) -> getAbbreviatedMonth(date.month)
            else -> resources.getString(
                R.string.year_abbreviated_month,
                date.year,
                getAbbreviatedMonth(date.month)
            )
        }
    }

    fun formatMonth(date: LocalDate): String {
        val today = LocalDate.now()
        return when {
            date.sameYearAs(today) -> getAbbreviatedMonth(date.month)
            else -> resources.getString(
                R.string.year_abbreviated_month,
                date.year,
                getAbbreviatedMonth(date.month)
            )
        }
    }

    fun formatRelativeYear(year: Year): String {
        val thisYear = Year.now()
        return when {
            year.value == thisYear.value -> resources.getString(R.string.this_year)
            else -> year.value.toString()
        }
    }

    fun formatRelativeYear(date: LocalDate): String {
        val thisYear = Year.now()
        return when {
            date.year == thisYear.value -> resources.getString(R.string.this_year)
            date.year + 1 == thisYear.value -> resources.getString(R.string.last_year)
            else -> date.year.toString()
        }
    }

    fun formatDateRange(from: LocalDate, to: LocalDate): String {
        val today = LocalDate.now()
        return when {
            from.sameMonthAs(to) && from.sameYearAs(today) -> resources.getString(
                R.string.abbreviated_month_date_range,
                getAbbreviatedMonth(from.month),
                from.dayOfMonth,
                to.dayOfMonth,
            )

            from.sameYearAs(to) && from.sameYearAs(today) -> buildFormattedDateRange(
                getAbbreviatedMonthDate(from),
                getAbbreviatedMonthDate(to),
            )

            from.sameMonthAs(to) -> resources.getString(
                R.string.year_abbreviated_month_date_range,
                from.year,
                getAbbreviatedMonth(from.month),
                from.dayOfMonth,
                to.dayOfMonth,
            )

            from.sameYearAs(to) -> resources.getString(
                R.string.year_different_abbreviated_month_date_range,
                from.year,
                getAbbreviatedMonth(from.month),
                from.dayOfMonth,
                getAbbreviatedMonth(to.month),
                to.dayOfMonth,
            )

            else -> buildFormattedDateRange(
                getYearAbbreviatedMonthDate(from),
                getYearAbbreviatedMonthDate(to)
            )
        }
    }

    fun getYearAbbreviatedMonthDate(date: LocalDate): String = resources.getString(
        R.string.year_abbreviated_month_date,
        date.year,
        getAbbreviatedMonth(date.month),
        date.dayOfMonth,
    )

    private fun getYearAbbreviatedMonth(date: LocalDate): String =
        resources.getString(
            R.string.year_abbreviated_month,
            date.year,
            getAbbreviatedMonth(date.month)
        )

    fun getAbbreviatedMonthDate(date: LocalDate): String = resources.getString(
        R.string.abbreviated_months_dates,
        getAbbreviatedMonth(date.month),
        date.dayOfMonth,
    )

    private fun getAbbreviatedMonth(month: Month): String =
        resources.getStringArray(R.array.abbreviated_months)[month.value - 1]

    fun formatDateRangeByStyle(from: LocalDate, to: LocalDate, formatStyle: FormatStyle): String {
        val today = LocalDate.now()
        return when (formatStyle) {
            FormatStyle.DATE -> formatDateRange(from, to)

            FormatStyle.MONTH -> when {
                from.sameMonthAs(to) && from.sameYearAs(today) -> getAbbreviatedMonth(from.month)
                from.sameYearAs(to) && from.sameYearAs(today) ->
                    buildFormattedDateRange(
                        getAbbreviatedMonth(from.month),
                        getAbbreviatedMonth(to.month)
                    )

                from.sameMonthAs(to) -> getYearAbbreviatedMonth(from)
                else -> buildFormattedDateRange(
                    getYearAbbreviatedMonth(from),
                    getYearAbbreviatedMonth(to)
                )
            }

            FormatStyle.YEAR -> when {
                from.sameYearAs(to) -> from.year.toString()
                else -> buildFormattedDateRange(from.year.toString(), to.year.toString())
            }
        }
    }

    private fun buildFormattedDateRange(from: String, to: String) =
        resources.getString(R.string.various_date_range, from, to)

    private companion object {
        fun LocalDate.sameYearAs(other: LocalDate): Boolean = year == other.year

        fun LocalDate.sameMonthAs(other: LocalDate): Boolean =
            sameYearAs(other) && month == other.month

        fun LocalDate.lastMonthOf(other: LocalDate): Boolean = plusMonths(1L).sameMonthAs(other)
    }
}

enum class FormatStyle {
    DATE,
    MONTH,
    YEAR,
    ;
}
