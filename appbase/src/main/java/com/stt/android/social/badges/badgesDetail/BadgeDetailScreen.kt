package com.stt.android.social.badges.badgesDetail

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.zIndex
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.component.SuuntoCard
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.SuuntoIcon
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.data.badges.BadgeStatus
import com.stt.android.data.badges.ExploreMore
import java.text.DateFormat
import java.util.Date

@Composable
internal fun BadgeDetailScreen(
    viewData: BadgesDetailViewData.Loaded,
    onEvent: (BadgeDetailViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val scrollState = rememberLazyListState()
    val nestedScrollConnection = rememberNestedScrollConnection(scrollState)
    val backgroundImage: String? = viewData.badgesDetail.badgeBackgroundImageUrl
    val badgeImage: String? = viewData.badgesDetail.acquiredBadgeIconUrl
    val badgeImageNot: String? = viewData.badgesDetail.badgeIconUrl
    val badgesId =
        if (viewData.badgesDetail.badgeStatus == BadgeStatus.ACQUIRED) badgeImage else badgeImageNot

    val density = LocalDensity.current
    val statusBarHeight = WindowInsets.systemBars.getTop(density)
    val backgroundHeight = 200.dp
    val topBarHeight = 56.dp
    val maxBadgeSize = 148.dp
    val minBadgeSize = 48.dp
    val badgeInitialOffsetY = backgroundHeight - maxBadgeSize / 2
    val headerHeight = backgroundHeight + maxBadgeSize / 2
    val headerHeightPx = with(density) { headerHeight.toPx() }
    val scrollY by remember {
        derivedStateOf {
            scrollState.firstVisibleItemIndex * headerHeightPx + scrollState.firstVisibleItemScrollOffset
        }
    }
    val maxScrollPx = with(density) { (backgroundHeight - topBarHeight).toPx() }
    val progress by remember {
        derivedStateOf {
            (scrollY / maxScrollPx).coerceIn(0f, 1f)
        }
    }
    val topBarTotalHeight = topBarHeight + with(density) { statusBarHeight.toDp() }

    Box(
        modifier = modifier
            .fillMaxSize()
            .nestedScroll(nestedScrollConnection)
    ) {
        if (backgroundImage != null) {
            AsyncImage(
                model = backgroundImage,
                contentDescription = "background_Image",
                modifier = Modifier
                    .fillMaxWidth()
                    .height(backgroundHeight)
                    .zIndex(0f),
                contentScale = ContentScale.Crop,
                alpha = 1f - progress
            )
        }
        LazyColumn(
            state = scrollState, modifier = Modifier
                .fillMaxSize()
                .padding(top = topBarTotalHeight)
        ) {
            item {
                Spacer(modifier = Modifier.height(headerHeight - topBarTotalHeight))
            }
            item {
                Column(
                    modifier = Modifier.padding(
                            MaterialTheme.spacing.medium,
                        ), verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = viewData.badgesDetail.badgeName ?: "badges name null",
                        style = MaterialTheme.typography.bodyMegaBold,
                        color = MaterialTheme.colorScheme.nearBlack,
                    )
                }
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            }
            item {
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            }
            when (viewData.badgesDetail.badgeStatus) {
                BadgeStatus.ACQUIRED -> {
                    item {
                        BadgeProgressRow(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                    item {
                        BadgeRankingSummary(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                    item {
                        BadgeTimeRangeSummary(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                    item {
                        BadgeAcquisitionTimeSummary(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                    item {
                        AchievementListComponent(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                }

                BadgeStatus.IN_PROGRESS -> {
                    item {
                        BadgeProgressRow(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                    item {
                        BadgeTimeRangeSummary(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                }

                BadgeStatus.NOT_STARTED -> {
                    item {
                        BadgeProgressRow(viewData)
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                    item {
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                }

                null -> {
                    item {
                        Text("something wrong with BadgeStatus ")
                        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    }
                }
            }
            item {
                BadgesDetailIntroduction(viewData)
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                ExploreMoreItems(viewData, onEvent = onEvent)
            }
        }
        TransparentTopBar(
            title = "",
            onNavigationClick = { onEvent(BadgeDetailViewEvent.Close) },
            enabledSharing = when (viewData.badgesDetail.badgeStatus) {
                BadgeStatus.ACQUIRED -> true
                BadgeStatus.IN_PROGRESS -> false
                BadgeStatus.NOT_STARTED -> false
                null -> false
            },
            modifier = Modifier.align(Alignment.TopCenter),
            onEvent = onEvent,
        )

        if (badgesId != null) {
            val badgeSize = lerp(maxBadgeSize, minBadgeSize, progress)
            val badgeOffsetY = lerp(
                badgeInitialOffsetY,
                topBarHeight / 2 - minBadgeSize / 2 + with(density) { statusBarHeight.toDp() },
                progress
            )
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .zIndex(3f)
            ) {
                AsyncImage(
                    model = badgesId,
                    contentDescription = "badge_Image",
                    modifier = Modifier
                        .size(badgeSize)
                        .align(Alignment.TopCenter)
                        .offset(y = badgeOffsetY),
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}

private fun lerp(start: Dp, end: Dp, fraction: Float): Dp {
    return start + (end - start) * fraction
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TransparentTopBar(
    onEvent: (BadgeDetailViewEvent) -> Unit,
    onNavigationClick: () -> Unit,
    modifier: Modifier = Modifier,
    title: String = "",
    enabledSharing: Boolean = false,
    icon: SuuntoIcon = SuuntoIcons.ActionBack,
) {
    TopAppBar(
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurface
            )
        },
        navigationIcon = {
            SuuntoIconButton(
                icon = icon,
                onClick = onNavigationClick,
            )
        },
        actions = {
            if (enabledSharing) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_share),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(MaterialTheme.spacing.medium)
                        .clickable { onEvent(BadgeDetailViewEvent.Share) })
            }
        },
        modifier = modifier,
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent,
            titleContentColor = MaterialTheme.colorScheme.onSurface,
            navigationIconContentColor = MaterialTheme.colorScheme.onSurface,
        ),
    )
}

@Composable
private fun rememberNestedScrollConnection(
    scrollState: LazyListState
): NestedScrollConnection = remember(scrollState) {
    object : NestedScrollConnection {
        override fun onPreScroll(
            available: Offset, source: NestedScrollSource
        ): Offset {
            return Offset.Zero
        }

        override fun onPostScroll(
            consumed: Offset, available: Offset, source: NestedScrollSource
        ): Offset {
            return Offset.Zero
        }
    }
}

@Composable
fun BadgeProgressRow(loaded: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.padding(MaterialTheme.spacing.medium)
    ) {
        Icon(
            painter = painterResource(id = R.drawable.summary_icon),
            contentDescription = null,
            modifier = Modifier.size(24.dp)
        )
        val progressList = loaded.conditionData.map { item ->
            "${item.current} of ${item.target}"
        }
        val progressText = progressList.joinToString(", ")
        Text(progressText, color = MaterialTheme.colorScheme.nearBlack)
    }
}

@Composable
fun BadgeRankingSummary(userBadge: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier) {
    val ranking = userBadge.badgesDetail.acquisitionRanking
    if (ranking != null && ranking > 0) {
        val text = stringResource(R.string.badges_earned_ranking, ranking.toString())
        Row(
            modifier = modifier, verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ranking_summary),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Text(
                text,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }
    }
}

@Composable
fun BadgeTimeRangeSummary(viewData: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier) {
    val userBadge = viewData.badgesDetail
    val start = userBadge.startTime
    val end = userBadge.endTime
    val configuration = LocalConfiguration.current
    val locale = configuration.locales[0]
    if (start != null && end != null) {
        val startDate = Date(start)
        val endDate = Date(end)
        val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM, locale)
        val text = "${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}"
        Row(
            modifier = modifier, verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.time_range_summary),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Text(
                text,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }
    }
}

@Composable
fun BadgeAcquisitionTimeSummary(
    viewData: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier
) {
    val userBadge = viewData.badgesDetail
    val acquisitionTime = userBadge.acquisitionTime
    if (acquisitionTime != null) {
        val date = Date(acquisitionTime)
        val configuration = LocalConfiguration.current
        val locale = configuration.locales[0]
        val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM, locale)
        val dateStr = dateFormat.format(date)
        val text = stringResource(R.string.acquisition_time_summary_badges, dateStr)
        Row(
            modifier = modifier, verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.acquisition_time_summary),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Text(
                text,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }
    }
}

@Composable
internal fun BadgesDetailIntroduction(
    viewData: BadgesDetailViewData.Loaded,
    modifier: Modifier = Modifier,
) {
    SuuntoCard(
        modifier = modifier.padding(MaterialTheme.spacing.medium),
    ) {
        Column(
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = R.string.description_badges),
                    color = MaterialTheme.colorScheme.nearBlack,
                    style = MaterialTheme.typography.bodyLargeBold,
                )
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.spacing.small),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = viewData.badgesDetail.badgeDesc ?: "badges desc null",
                    color = MaterialTheme.colorScheme.nearBlack,
                    style = MaterialTheme.typography.bodyLarge,
                )
            }
        }
    }
}

@Composable
private fun ExploreMoreItems(
    viewData: BadgesDetailViewData.Loaded,
    onEvent: (BadgeDetailViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    SuuntoCard(
        modifier = modifier.padding(MaterialTheme.spacing.medium),
    ) {
        Column(
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = R.string.explore_more_badges),
                    color = MaterialTheme.colorScheme.nearBlack,
                    style = MaterialTheme.typography.bodyLargeBold,
                )
            }
            ExploreMoreContent(viewData, onEvent = onEvent)
        }
    }
}

@Composable
private fun ExploreMoreContent(
    badges: BadgesDetailViewData.Loaded,
    onEvent: (BadgeDetailViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        badges.exploreMore.forEach { badge ->
            ExploreMoreBadgeItem(
                badge = badge, onEvent = onEvent
            )
        }
    }
}

@Composable
private fun ExploreMoreBadgeItem(
    badge: ExploreMore, onEvent: (BadgeDetailViewEvent) -> Unit
) {
    Column(
        modifier = Modifier.width(80.dp), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val imageUrl = badge.badgeIconUrl
        Box(
            modifier = Modifier
                .size(80.dp)
                .background(
                    shape = MaterialTheme.shapes.medium, color = MaterialTheme.colorScheme.surface
                )
                .clickable {
                    badge.badgeConfigId?.let {
                        BadgeDetailViewEvent.OnExploreBadgesClick(
                            it
                        )
                    }?.let { onEvent(it) }
                },

            contentAlignment = Alignment.Center
        ) {
            if (!imageUrl.isNullOrBlank()) {
                AsyncImage(
                    model = imageUrl,
                    contentDescription = badge.badgeName,
                    modifier = Modifier.size(80.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = badge.badgeName ?: "",
            style = MaterialTheme.typography.body,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.nearBlack,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun AchievementListComponent(
    viewData: BadgesDetailViewData.Loaded,

    ) {
    val badgesAchievementDataList = viewData.badgesAchievementDataList
    if (!badgesAchievementDataList.isNullOrEmpty()) {
        Card(
            modifier = Modifier.padding(top = MaterialTheme.spacing.large),
            shape = MaterialTheme.shapes.large,
            border = BorderStroke(
                MaterialTheme.spacing.xxxsmall, MaterialTheme.colorScheme.dividerColor
            ),
            colors = CardDefaults.cardColors()
                .copy(containerColor = MaterialTheme.colorScheme.dividerColor)
        ) {
            // achievement data is limited to 4 items
            val limitedAchievementData = badgesAchievementDataList.take(4)
            // because I implement the grid divider line by using the grid item spacing.
            // I need to fill the LazyVerticalGrid to avoid the last item background is divider color
            val fillContentData = if (limitedAchievementData.size % 2 == 0) {
                limitedAchievementData
            } else {
                limitedAchievementData + BadgesAchievementData(
                    AnnotatedString(""), ""
                )
            }
            LazyVerticalGrid(
                modifier = Modifier.heightIn(max = 108.dp),
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxxsmall),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxxsmall),
            ) {
                items(fillContentData.size) { index ->
                    val item = fillContentData[index]
                    Column(
                        modifier = Modifier
                            .background(MaterialTheme.colorScheme.surface)
                            .height(54.dp)
                            .padding(horizontal = MaterialTheme.spacing.medium),
                        verticalArrangement = Arrangement.Center,
                    ) {
                        Text(
                            text = item.explanation,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                        Text(
                            text = item.value,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
            HorizontalDivider()
            ActivityIconListCom(viewData = viewData)
        }
    }
}

@Composable
private fun ActivityIconListCom(
    viewData: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier
) {
    val activityIds = viewData.badgesDetail.activityIds
    if (!activityIds.isNullOrEmpty()) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.surface)
                .padding(vertical = MaterialTheme.spacing.medium)
        ) {
            activityIds.take(9).forEach {
                SuuntoActivityIcon(
                    it,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall),
                    iconSize = MaterialTheme.iconSizes.small
                )
            }
            if (activityIds.size > 9) {
                Icon(
                    imageVector = Icons.Filled.MoreVert,
                    contentDescription = null,
                    modifier = Modifier.rotate(90f)
                )
            }
        }
    }
}
