package com.stt.android.home.dashboardv2.edit.ui.components.addwidget

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.dashboardv2.edit.AddWidgetViewEvent
import com.stt.android.home.dashboardv2.ui.widgets.Widget
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.dashboardv2.widgets.dataloader.WidgetData

@Suppress("FunctionName")
internal fun LazyListScope.WidgetSection(
    widgets: List<WidgetData<*>>,
    onEvent: (event: AddWidgetViewEvent) -> Unit,
) {
    items(
        items = widgets,
        key = { widget -> widget.widgetType },
    ) { widgetData ->
        Column(
            modifier = Modifier
                .animateItem(),
        ) {
            Row(
                modifier = Modifier
                    .padding(MaterialTheme.spacing.medium),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Box(
                    modifier = Modifier.requiredSize(96.dp)
                ) {
                    Widget(
                        widgetData = widgetData,
                        modifier = Modifier
                            .requiredSize(192.dp)
                            .scale(0.5F),
                    )
                }

                Column(
                    modifier = Modifier
                        .weight(1.0F)
                        .padding(start = MaterialTheme.spacing.medium),
                ) {
                    Text(
                        text = stringResource(id = widgetData.titleRes()),
                        style = MaterialTheme.typography.bodyBold,
                    )

                    Text(
                        text = stringResource(id = widgetData.descriptionRes()),
                        modifier = Modifier.padding(vertical = MaterialTheme.spacing.small),
                        style = MaterialTheme.typography.body,
                    )

                    if (!widgetData.availableWithAllDevices()) {
                        Text(
                            text = stringResource(id = R.string.dashboard_widget_type_not_available_on_all_devices_new),
                            style = MaterialTheme.typography.body,
                            color = MaterialTheme.colorScheme.darkGrey,
                        )
                    }
                }

                AddWidgetButton(
                    onClick = { onEvent(AddWidgetViewEvent.AddWidget(widgetData.widgetType)) },
                )
            }

            HorizontalDivider(
                color = MaterialTheme.colorScheme.lightGrey,
            )
        }
    }
}

@Composable
private fun AddWidgetButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    IconButton(
        onClick = onClick,
        modifier = modifier
            .padding(start = MaterialTheme.spacing.medium),
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_plus),
            contentDescription = null,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.large)
                .background(MaterialTheme.colorScheme.primary)
                .padding(MaterialTheme.spacing.small),
            tint = Color.White,
        )
    }
}

@StringRes
private fun WidgetData<*>.titleRes(): Int = when (widgetType) {
    WidgetType.ASCENT -> R.string.dashboard_widget_ascent_name
    WidgetType.CALENDAR_THIS_WEEK -> R.string.dashboard_widget_calendar_this_week_name
    WidgetType.CALENDAR_THIS_MONTH -> R.string.dashboard_widget_calendar_this_month_name
    WidgetType.CALENDAR_LAST_30_DAYS -> R.string.dashboard_widget_calendar_last_30_days_name
    WidgetType.CALORIES -> R.string.dashboard_widget_calories_name
    WidgetType.COMMUTE_THIS_MONTH -> R.string.dashboard_widget_commute_this_month_name_new
    WidgetType.DAILY_HEART_RATE -> R.string.dashboard_widget_daily_heart_rate_name
    WidgetType.DISTANCE_OF_CYCLING_THIS_WEEK -> R.string.dashboard_widget_cycling_distance_this_week_name
    WidgetType.DISTANCE_OF_HIKING_THIS_WEEK -> R.string.dashboard_widget_hiking_distance_this_week_name
    WidgetType.DISTANCE_OF_RUNNING_THIS_WEEK -> R.string.dashboard_widget_running_distance_this_week_name
    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_WEEK -> R.string.dashboard_widget_activities_this_week_name_new
    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH -> R.string.dashboard_widget_activities_this_month_name_new
    WidgetType.DURATION_BY_ACTIVITY_GROUP_LAST_30_DAYS -> R.string.dashboard_widget_activities_last_30_days_name_new
    WidgetType.DURATION_OF_DIVING_THIS_WEEK -> R.string.dashboard_widget_dive_duration_this_week_name
    WidgetType.MAP_THIS_WEEK -> R.string.dashboard_widget_map_this_week_name
    WidgetType.MAP_THIS_MONTH -> R.string.dashboard_widget_map_this_month_name
    WidgetType.MAP_LAST_30_DAYS -> R.string.dashboard_widget_map_last_30_days_name
    WidgetType.MINIMUM_HEART_RATE -> R.string.dashboard_widget_minimum_heart_rate_name
    WidgetType.PROGRESS -> R.string.dashboard_widget_progress_name
    WidgetType.RECOVERY -> R.string.dashboard_widget_recovery_name
    WidgetType.RESOURCES -> R.string.dashboard_widget_resources_name
    WidgetType.SLEEP -> R.string.dashboard_widget_sleep_name
    WidgetType.SLEEP_HRV -> R.string.dashboard_widget_hrv_name
    WidgetType.STEPS -> R.string.dashboard_widget_steps_name
    WidgetType.TOTAL_DURATION_THIS_WEEK -> R.string.dashboard_widget_duration_this_week_name
    WidgetType.TOTAL_DURATION_LAST_7_DAYS -> R.string.dashboard_widget_duration_last_7_days_name
    WidgetType.TOTAL_DURATION_THIS_MONTH -> R.string.dashboard_widget_duration_this_month_name
    WidgetType.TOTAL_DURATION_LAST_30_DAYS -> R.string.dashboard_widget_duration_last_30_days_name
    WidgetType.TSS -> R.string.widget_tss_header
    WidgetType.VO2MAX -> R.string.dashboard_widget_vo2max_name
    WidgetType.PACE_OF_RUNNING_THIS_WEEK -> R.string.dashboard_widget_running_pace_this_week_name
    WidgetType.SPEED_OF_CYCLING_THIS_WEEK -> R.string.dashboard_widget_cycling_speed_this_week_name
    WidgetType.PACE_OF_HIKING_THIS_WEEK -> R.string.dashboard_widget_hiking_pace_this_week_name
    WidgetType.TIMES_OF_DIVING_THIS_WEEK -> R.string.dashboard_widget_dive_times_this_week_name
    WidgetType.MENSTRUAL_PERIOD -> R.string.dashboard_widget_menstrual_period_name
    WidgetType.MINIMUM_SLEEP_HEART_RATE -> R.string.dashboard_widget_minimum_sleep_heart_rate_name
    WidgetType.RESTING_HEART_RATE -> R.string.dashboard_widget_resting_heart_rate_name
}

@StringRes
private fun WidgetData<*>.descriptionRes(): Int = when (widgetType) {
    WidgetType.ASCENT -> R.string.dashboard_widget_description_ascent_new
    WidgetType.CALENDAR_THIS_WEEK -> R.string.dashboard_widget_description_calendar_this_week_new
    WidgetType.CALENDAR_THIS_MONTH -> R.string.dashboard_widget_description_calendar_this_month_new
    WidgetType.CALENDAR_LAST_30_DAYS -> R.string.dashboard_widget_description_calendar_last_30_days_new
    WidgetType.CALORIES -> R.string.dashboard_widget_description_calories_new
    WidgetType.COMMUTE_THIS_MONTH -> R.string.dashboard_widget_description_commute_this_month_new
    WidgetType.DAILY_HEART_RATE -> R.string.dashboard_widget_description_daily_heart_rate
    WidgetType.DISTANCE_OF_CYCLING_THIS_WEEK -> R.string.dashboard_widget_description_cycling_distance_this_week
    WidgetType.DISTANCE_OF_HIKING_THIS_WEEK -> R.string.dashboard_widget_description_hiking_distance_this_week
    WidgetType.DISTANCE_OF_RUNNING_THIS_WEEK -> R.string.dashboard_widget_description_running_distance_this_week
    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_WEEK -> R.string.dashboard_widget_description_activities_this_week_new
    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH -> R.string.dashboard_widget_description_activities_this_month_new
    WidgetType.DURATION_BY_ACTIVITY_GROUP_LAST_30_DAYS -> R.string.dashboard_widget_description_activities_last_30_days_new
    WidgetType.DURATION_OF_DIVING_THIS_WEEK -> R.string.dashboard_widget_description_dive_duration_this_week
    WidgetType.MAP_THIS_WEEK -> R.string.dashboard_widget_description_map_this_week_new
    WidgetType.MAP_THIS_MONTH -> R.string.dashboard_widget_description_map_this_month_new
    WidgetType.MAP_LAST_30_DAYS -> R.string.dashboard_widget_description_map_last_30_days_new
    WidgetType.MINIMUM_HEART_RATE -> R.string.dashboard_widget_description_minimum_heart_rate_new
    WidgetType.PROGRESS -> R.string.dashboard_widget_description_progress_new
    WidgetType.RECOVERY -> R.string.dashboard_widget_description_recovery
    WidgetType.RESOURCES -> R.string.dashboard_widget_description_resources_new
    WidgetType.SLEEP -> R.string.dashboard_widget_description_sleep_new
    WidgetType.SLEEP_HRV -> R.string.dashboard_widget_sleep_hrv_description_new
    WidgetType.STEPS -> R.string.dashboard_widget_description_steps_new
    WidgetType.TOTAL_DURATION_THIS_WEEK -> R.string.dashboard_widget_description_duration_this_week_new
    WidgetType.TOTAL_DURATION_LAST_7_DAYS -> R.string.dashboard_widget_description_training_new
    WidgetType.TOTAL_DURATION_THIS_MONTH -> R.string.dashboard_widget_description_duration_this_month_new
    WidgetType.TOTAL_DURATION_LAST_30_DAYS -> R.string.dashboard_widget_description_duration_last_30_days_new
    WidgetType.TSS -> R.string.dashboard_widget_description_tss
    WidgetType.VO2MAX -> R.string.dashboard_widget_description_vo2max
    WidgetType.PACE_OF_RUNNING_THIS_WEEK -> R.string.dashboard_widget_description_running_pace_this_week
    WidgetType.SPEED_OF_CYCLING_THIS_WEEK -> R.string.dashboard_widget_description_cycling_speed_this_week
    WidgetType.PACE_OF_HIKING_THIS_WEEK -> R.string.dashboard_widget_description_hiking_pace_this_week
    WidgetType.TIMES_OF_DIVING_THIS_WEEK -> R.string.dashboard_widget_description_dive_times_this_week
    WidgetType.MENSTRUAL_PERIOD -> R.string.dashboard_widget_description_menstrual_period
    WidgetType.MINIMUM_SLEEP_HEART_RATE -> R.string.dashboard_widget_description_minimum_sleep_heart_rate
    WidgetType.RESTING_HEART_RATE -> R.string.dashboard_widget_description_resting_heart_rate
}

private fun WidgetData<*>.availableWithAllDevices(): Boolean = when (widgetType) {
    WidgetType.ASCENT -> true
    WidgetType.CALENDAR_THIS_WEEK -> true
    WidgetType.CALENDAR_THIS_MONTH -> true
    WidgetType.CALENDAR_LAST_30_DAYS -> true
    WidgetType.CALORIES -> false
    WidgetType.COMMUTE_THIS_MONTH -> true
    WidgetType.DAILY_HEART_RATE -> false
    WidgetType.DISTANCE_OF_CYCLING_THIS_WEEK -> true
    WidgetType.DISTANCE_OF_HIKING_THIS_WEEK -> true
    WidgetType.DISTANCE_OF_RUNNING_THIS_WEEK -> true
    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_WEEK -> true
    WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH -> true
    WidgetType.DURATION_BY_ACTIVITY_GROUP_LAST_30_DAYS -> true
    WidgetType.DURATION_OF_DIVING_THIS_WEEK -> true
    WidgetType.MAP_THIS_WEEK -> true
    WidgetType.MAP_THIS_MONTH -> true
    WidgetType.MAP_LAST_30_DAYS -> true
    WidgetType.MINIMUM_HEART_RATE -> false
    WidgetType.PROGRESS -> true
    WidgetType.RECOVERY -> false
    WidgetType.RESOURCES -> false
    WidgetType.SLEEP -> false
    WidgetType.SLEEP_HRV -> false
    WidgetType.STEPS -> false
    WidgetType.TOTAL_DURATION_THIS_WEEK -> true
    WidgetType.TOTAL_DURATION_LAST_7_DAYS -> true
    WidgetType.TOTAL_DURATION_THIS_MONTH -> true
    WidgetType.TOTAL_DURATION_LAST_30_DAYS -> true
    WidgetType.TSS -> true
    WidgetType.VO2MAX -> true
    WidgetType.PACE_OF_RUNNING_THIS_WEEK -> true
    WidgetType.SPEED_OF_CYCLING_THIS_WEEK -> true
    WidgetType.PACE_OF_HIKING_THIS_WEEK -> true
    WidgetType.TIMES_OF_DIVING_THIS_WEEK -> true
    WidgetType.MENSTRUAL_PERIOD -> true
    WidgetType.MINIMUM_SLEEP_HEART_RATE -> false
    WidgetType.RESTING_HEART_RATE -> false
}

@Preview
@Composable
private fun AddWidgetButtonPreview() {
    M3AppTheme {
        AddWidgetButton(
            onClick = {},
        )
    }
}
