package com.stt.android.home.dashboardv2.repository

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.annotations.ProtectWeakReference
import com.stt.android.di.DashboardPreferences
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import org.json.JSONArray
import org.json.JSONException
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
internal class DashboardConfigRepository @Inject constructor(
    @DashboardPreferences private val dashboardPreferences: SharedPreferences,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
) {
    // DO NOT change the order. This is the order we are going to show in the "add widgets" UI.
    private val availableWidgetTypes: List<WidgetType> get() = buildList {
        val newWidgetsEnabled = featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS,
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS_DEFAULT,
        )

        if (FlavorUtils.isSuuntoApp) {
            add(WidgetType.DAILY_HEART_RATE)
            if (newWidgetsEnabled) {
                // Hide for now, because the rest hr data is not reliable.
                // add(WidgetType.RESTING_HEART_RATE)
            }
            add(WidgetType.MINIMUM_HEART_RATE)
            add(WidgetType.MINIMUM_SLEEP_HEART_RATE)
            add(WidgetType.SLEEP)
            add(WidgetType.SLEEP_HRV)
            add(WidgetType.RECOVERY)
        }
        add(WidgetType.PROGRESS)
        if (FlavorUtils.isSuuntoApp) {
            add(WidgetType.VO2MAX)
        }
        add(WidgetType.TOTAL_DURATION_THIS_WEEK)
        add(WidgetType.TOTAL_DURATION_LAST_7_DAYS)
        add(WidgetType.TOTAL_DURATION_THIS_MONTH)
        add(WidgetType.TOTAL_DURATION_LAST_30_DAYS)
        add(WidgetType.ASCENT)
        add(WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_WEEK)
        add(WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_MONTH)
        add(WidgetType.DURATION_BY_ACTIVITY_GROUP_LAST_30_DAYS)
        add(WidgetType.CALENDAR_THIS_WEEK)
        add(WidgetType.CALENDAR_THIS_MONTH)
        add(WidgetType.CALENDAR_LAST_30_DAYS)
        add(WidgetType.MAP_THIS_WEEK)
        add(WidgetType.MAP_THIS_MONTH)
        add(WidgetType.MAP_LAST_30_DAYS)
        add(WidgetType.TSS)
        if (FlavorUtils.isSuuntoApp) {
            add(WidgetType.STEPS)
            add(WidgetType.CALORIES)
            add(WidgetType.RESOURCES)
        }
        add(WidgetType.COMMUTE_THIS_MONTH)
        if (FlavorUtils.isSuuntoApp) {
            if (newWidgetsEnabled) {
                add(WidgetType.MENSTRUAL_PERIOD)
            }
        }
        if (newWidgetsEnabled) {
            add(WidgetType.PACE_OF_RUNNING_THIS_WEEK)
            add(WidgetType.DISTANCE_OF_RUNNING_THIS_WEEK)
            add(WidgetType.SPEED_OF_CYCLING_THIS_WEEK)
            add(WidgetType.DISTANCE_OF_CYCLING_THIS_WEEK)
            add(WidgetType.PACE_OF_HIKING_THIS_WEEK)
            add(WidgetType.DISTANCE_OF_HIKING_THIS_WEEK)
            add(WidgetType.TIMES_OF_DIVING_THIS_WEEK)
            add(WidgetType.DURATION_OF_DIVING_THIS_WEEK)
        }
    }

    private val _selectedWidgets: MutableStateFlow<List<WidgetType>> = MutableStateFlow(emptyList())
    val selectedWidgets: Flow<List<WidgetType>> = _selectedWidgets.asStateFlow()
    val unselectedWidgets: Flow<List<WidgetType>> = selectedWidgets.map { selected ->
        availableWidgetTypes - selected.toSet()
    }

    private val _showLatestWorkout: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val showLatestWorkout: StateFlow<Boolean> = _showLatestWorkout.asStateFlow()

    private val _latestDismissedWorkoutId: MutableStateFlow<Int?> = MutableStateFlow(0)
    val latestDismissedWorkoutId: StateFlow<Int?> = _latestDismissedWorkoutId.asStateFlow()

    private val _tabIndex: MutableStateFlow<Int> = MutableStateFlow(
        dashboardPreferences.getInt(STTConstants.DashboardPreferences.KEY_LAST_DASHBOARD_TAB, 0)
    )
    val tabIndex: StateFlow<Int> = _tabIndex.asStateFlow()

    private val _dashboardTutorialShown: MutableStateFlow<Boolean> = MutableStateFlow(
        dashboardPreferences.getBoolean(STTConstants.DashboardPreferences.KEY_DASHBOARD_V2_INTRO_SHOWN, false)
    )
    val dashboardTutorialShown: StateFlow<Boolean> = _dashboardTutorialShown.asStateFlow()

    // SharedPreferences keeps listeners in a WeakHashMap, so we need to keep it in memory.
    @ProtectWeakReference
    private val dashboardPreferencesListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
        if (key == null ||
            key == STTConstants.DashboardPreferences.KEY_SELECTED_DASHBOARD_WIDGETS ||
            key == STTConstants.DashboardPreferences.KEY_SELECTED_DASHBOARD_WIDGETS_LEGACY) {
            loadSelectedWidgets()
        }
        if (key == null || key == STTConstants.DashboardPreferences.KEY_SHOW_LATEST_WORKOUT) {
            loadShowLatestWorkout()
        }
        if (key == null || key == STTConstants.DashboardPreferences.KEY_LATEST_DISMISSED_WORKOUT) {
            loadLatestDismissedWorkout()
        }
        if (key == null || key == STTConstants.DashboardPreferences.KEY_LAST_DASHBOARD_TAB) {
            _tabIndex.value = dashboardPreferences.getInt(STTConstants.DashboardPreferences.KEY_LAST_DASHBOARD_TAB, 0)
        }
        if (key == null || key == STTConstants.DashboardPreferences.KEY_DASHBOARD_V2_INTRO_SHOWN) {
            _dashboardTutorialShown.value =
                dashboardPreferences.getBoolean(STTConstants.DashboardPreferences.KEY_DASHBOARD_V2_INTRO_SHOWN, false)
        }
    }

    init {
        dashboardPreferences.registerOnSharedPreferenceChangeListener(dashboardPreferencesListener)
        loadSelectedWidgets()
        loadShowLatestWorkout()
        loadLatestDismissedWorkout()
    }

    private fun loadSelectedWidgets() {
        val jsonString = dashboardPreferences.getString(STTConstants.DashboardPreferences.KEY_SELECTED_DASHBOARD_WIDGETS, null)
            ?: dashboardPreferences.getString(STTConstants.DashboardPreferences.KEY_SELECTED_DASHBOARD_WIDGETS_LEGACY, null)
            ?: run {
                updateSelectedWidgets(defaultWidgetTypes(FlavorUtils.isSuuntoApp))
                return
            }
        try {
            JSONArray(jsonString)
                .flatten()
                .mapNotNull(WidgetType::fromDbName)
                .distinct()
                .let { selectedWidgets -> _selectedWidgets.value = selectedWidgets }
        } catch (e: JSONException) {
            Timber.w(e, "Error while parsing selected widgets, raw string: %s", jsonString)
        }
    }

    private fun loadShowLatestWorkout() {
        _showLatestWorkout.tryEmit(
            dashboardPreferences.getBoolean(STTConstants.DashboardPreferences.KEY_SHOW_LATEST_WORKOUT, true)
        )
    }

    private fun loadLatestDismissedWorkout() {
        _latestDismissedWorkoutId.tryEmit(
            if (dashboardPreferences.contains(STTConstants.DashboardPreferences.KEY_LATEST_DISMISSED_WORKOUT)) {
                dashboardPreferences.getInt(STTConstants.DashboardPreferences.KEY_LATEST_DISMISSED_WORKOUT, 0)
            } else {
                null
            }
        )
    }

    fun updateSelectedWidgets(selected: List<WidgetType>) {
        try {
            val jsonString = selected.map(WidgetType::dbName)
                .toJsonArray()
                .let { array -> JSONArray().apply { put(array) } }
                .toString()
            dashboardPreferences.edit {
                putString(
                    STTConstants.DashboardPreferences.KEY_SELECTED_DASHBOARD_WIDGETS,
                    jsonString,
                )
            }
        } catch (e: JSONException) {
            Timber.w(e, "Error while serializing selected widgets")
        }
    }

    fun addWidget(toAdd: WidgetType) {
        _selectedWidgets.value
            .toMutableList()
            .apply { add(toAdd) }
            .let(::updateSelectedWidgets)
    }

    fun toggleShowLatestWorkout(toShow: Boolean) {
        dashboardPreferences.edit {
            putBoolean(STTConstants.DashboardPreferences.KEY_SHOW_LATEST_WORKOUT, toShow)
        }
    }

    fun updateLatestDismissedWorkout(id: Int?) {
        dashboardPreferences.edit {
            id?.let { putInt(STTConstants.DashboardPreferences.KEY_LATEST_DISMISSED_WORKOUT, it) }
                ?: remove(STTConstants.DashboardPreferences.KEY_LATEST_DISMISSED_WORKOUT)
        }
    }

    fun updateTabIndex(toBe: Int) {
        dashboardPreferences.edit {
            putInt(STTConstants.DashboardPreferences.KEY_LAST_DASHBOARD_TAB, toBe)
        }
    }

    fun markIntroductionAsShown() {
        dashboardPreferences.edit {
            putBoolean(STTConstants.DashboardPreferences.KEY_DASHBOARD_V2_INTRO_SHOWN, true)
        }
    }

    fun getLastReportedTop6Widgets(): List<WidgetType>? {
        val jsonString = dashboardPreferences.getString(STTConstants.DashboardPreferences.KEY_LAST_REPORTED_TOP6_WIDGETS, null)
            ?: return null
        
        return try {
            JSONArray(jsonString)
                .flatten()
                .mapNotNull(WidgetType::fromDbName)
                .distinct()
        } catch (e: JSONException) {
            Timber.w(e, "Error while parsing last reported top6 widgets, raw string: %s", jsonString)
            null
        }
    }

    fun updateLastReportedTop6Widgets(widgets: List<WidgetType>) {
        try {
            val jsonString = widgets.map(WidgetType::dbName)
                .toJsonArray()
                .let { array -> JSONArray().apply { put(array) } }
                .toString()
            dashboardPreferences.edit {
                putString(
                    STTConstants.DashboardPreferences.KEY_LAST_REPORTED_TOP6_WIDGETS,
                    jsonString,
                )
            }
        } catch (e: JSONException) {
            Timber.w(e, "Error while serializing last reported top6 widgets")
        }
    }

    private companion object {
        fun defaultWidgetTypes(isSuuntoApp: Boolean): List<WidgetType> = listOfNotNull(
            WidgetType.PROGRESS,
            WidgetType.SLEEP.takeIf { isSuuntoApp },
            WidgetType.CALENDAR_THIS_MONTH,
            WidgetType.MAP_THIS_MONTH,
            WidgetType.DAILY_HEART_RATE.takeIf { isSuuntoApp },
            WidgetType.TOTAL_DURATION_THIS_WEEK,
            WidgetType.DURATION_BY_ACTIVITY_GROUP_THIS_WEEK,
            WidgetType.ASCENT,
            WidgetType.SLEEP_HRV.takeIf { isSuuntoApp },
            WidgetType.MINIMUM_HEART_RATE.takeIf { isSuuntoApp },
            WidgetType.STEPS.takeIf { isSuuntoApp },
            WidgetType.CALORIES.takeIf { isSuuntoApp },
            WidgetType.RESOURCES.takeIf { isSuuntoApp },
            WidgetType.COMMUTE_THIS_MONTH,
        )

        fun JSONArray.flatten(): List<String> = buildList {
            forEachArray { array ->
                array.forEachString(::add)
            }
        }

        private inline fun JSONArray.forEachArray(action: (JSONArray) -> Unit) {
            repeat(length()) { i ->
                optJSONArray(i)?.let(action)
            }
        }

        private inline fun JSONArray.forEachString(action: (String) -> Unit) {
            repeat(length()) { i ->
                optString(i, null)?.let(action)
            }
        }

        fun List<String>.toJsonArray(): JSONArray = JSONArray().apply {
            forEach(::put)
        }
    }
}
