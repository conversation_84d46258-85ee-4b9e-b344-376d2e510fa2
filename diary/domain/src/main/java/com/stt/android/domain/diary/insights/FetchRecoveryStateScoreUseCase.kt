package com.stt.android.domain.diary.insights

import com.soy.algorithms.recovery.calculateDailyScore
import com.soy.algorithms.recovery.calculateRecoveryStateScore
import com.stt.android.coroutines.combine
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.diary.GetTrainingProgressDataUseCase
import com.stt.android.domain.diary.models.RecoveryStateContributors
import com.stt.android.domain.diary.models.RecoveryStateData
import com.stt.android.domain.diary.models.RecoveryStateGraphData
import com.stt.android.domain.diary.models.RecoveryZone
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.restheartrate.FetchRestHeartRateUseCase
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepRepository
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.user.UserSettingsDataSource
import com.stt.android.domain.workouts.feeling.DailyFeeling
import com.stt.android.domain.workouts.feeling.FetchDailyFeelingUseCase
import com.stt.android.utils.averageOfDouble
import com.stt.android.utils.averageOrNull
import com.stt.android.utils.takeIfNotNaN
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt

class FetchRecoveryStateScoreUseCase @Inject constructor(
    private val currentUserDataSource: CurrentUserDataSource,
    private val userSettingsDataSource: UserSettingsDataSource,
    private val sleepRepository: SleepRepository,
    private val fetchRecoveryDataUseCase: FetchRecoveryDataUseCase,
    private val trendDataRepository: TrendDataRepository,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val getTrainingProgressDataUseCase: GetTrainingProgressDataUseCase,
    private val fetchRestHeartRateUseCase: FetchRestHeartRateUseCase,
    private val fetchDailyFeelingUseCase: FetchDailyFeelingUseCase,
) {
    @IoThread
    operator fun invoke(
        fromDate: LocalDate,
        toDate: LocalDate,
        includeContributors: Boolean,
    ): Flow<List<RecoveryStateGraphData>> {
        return combine(
            getSleepHrvListByDateFlow(fromDate, toDate),
            getSleepListFlow(fromDate.minusDays(6), toDate),
            getTrendDataListFlow(fromDate, toDate),
            getProgressByDateFlow(fromDate, toDate),
            getRestHeartRateByDateFlow(fromDate, toDate),
            getRecoveryDataListFlow(fromDate, toDate),
            getFeelingByDateFlow(fromDate, toDate),
            getFeelingDistributionFlow(fromDate, toDate),
        ) { sleepHrvList, sleepList, trendDataList, progressByDate, restHeartRateByDate, recoveryDataList, feelingByDate, feelingDistribution ->
            val sleepListByDate =
                sleepList.groupBy { it.timestamp.toLocalDate() }
            val trendDataListByDate =
                trendDataList.groupBy { it.timestamp.toLocalDate() }
            val recoveryDataListByDate = recoveryDataList.groupBy { it.timestamp.toLocalDate() }
            val days = ChronoUnit.DAYS.between(fromDate, toDate).toInt()
            (0..days).mapNotNull { plusDays ->
                val date = fromDate.plusDays(plusDays.toLong())

                val last1DaySleep = sleepListByDate.getOrDefault(date, emptyList())
                val last7DaysSleep = (0..6).mapNotNull {
                    sleepListByDate.getOrDefault(
                        date.minusDays(it.toLong()),
                        emptyList(),
                    )
                }.flatten()

                val sleepHrv = sleepHrvList[date]?.firstOrNull()
                val hrvNormalRange = sleepHrv?.normalRange?.let {
                    it.start.roundToInt()..it.endInclusive.roundToInt()
                } ?: (0..0)
                val last1DayHrv = sleepHrv?.avgHrv?.roundToInt() ?: 0
                val last7DaysHrv = sleepHrv?.avg7DayHrv?.roundToInt() ?: 0
                val last1DayRestHR = restHeartRateByDate[date]?.restHeartRate?.inBpm?.roundToInt() ?: 0
                val last7DaysRestHRList = (0..6).mapNotNull {
                    restHeartRateByDate[date.minusDays(it.toLong())]
                        .takeIf { rhr -> (rhr?.restHeartRate?.inBpm?.roundToInt() ?: 0) > 0 }
                }
                val last7DaysRestHR = if (last7DaysRestHRList.isNotEmpty()) {
                    last7DaysRestHRList.averageOfDouble { it.restHeartRate.inBpm }
                        .takeIfNotNaN()
                        ?.roundToInt()
                        ?: 0
                } else {
                    0
                }

                val last1DayMinHR = calculateMinimumDaytimeHeartRate(
                    date, 
                    trendDataListByDate[date] ?: emptyList(),
                    sleepListByDate[date] ?: emptyList()
                )

                val last7DaysMinHRList = (0..6).mapNotNull {
                    val checkDate = date.minusDays(it.toLong())
                    val dayTrendData = trendDataListByDate[checkDate] ?: emptyList()
                    val daySleepData = sleepListByDate[checkDate] ?: emptyList()
                    calculateMinimumDaytimeHeartRate(checkDate, dayTrendData, daySleepData)
                        .takeIf { it > 0 }
                }
                
                val last7DaysMinHR = if (last7DaysMinHRList.isNotEmpty()) {
                    last7DaysMinHRList.average().roundToInt()
                } else {
                    0
                }

                val todayProgress = progressByDate[date]
                val yesterdayProgress = progressByDate[date.minusDays(1)]

                val feeling7Days = feelingByDate.getAvgFeelingForTimeRage(date, 7)
                val feeling42Days = feelingByDate.getAvgFeelingForTimeRage(date, 42)

                val dailyResources = recoveryDataListByDate
                    .getOrDefault(date, emptyList())
                    .map { it.balance }
                    .averageOrNull()
                    ?.let { (it * 100).roundToInt() }
                    ?: 0
                val dailySteps = trendDataListByDate
                    .getOrDefault(date, emptyList())
                    .sumOf { it.steps }
                val dailyScore = getDailyScore(
                    dailyResources = dailyResources,
                    dailySteps = dailySteps,
                )

                val sd1 = last1DaySleep.getAvgSleepDuration()
                val sd7 = last7DaysSleep.getAvgSleepDuration()
                val sd1TotalSeconds = last1DaySleep.getTotalSleepDurationInSeconds()
                val sd7TotalSeconds = last7DaysSleep.getAvgSleepDurationInSeconds()
                val tssy = yesterdayProgress?.exactTss?.roundToInt() ?: 0
                val tss1 = todayProgress?.exactTss?.roundToInt() ?: 0
                val tsb1 = todayProgress?.form?.roundToInt() ?: 0
                val ctl = todayProgress?.fitness?.roundToInt() ?: 0
                val yesterdayCtl = yesterdayProgress?.fitness?.roundToInt() ?: 0
                
                val daysInCurrentWeek = date.dayOfWeek.value
                val ctlPercent = if (ctl > 0 && daysInCurrentWeek > 0) {
                    (tss1.toFloat() / (ctl * daysInCurrentWeek)) * 100f
                } else {
                    0f
                }
                
                val yesterdayDate = date.minusDays(1)
                val yesterdayDaysInWeek = yesterdayDate.dayOfWeek.value
                val yesterdayCtlPercent = if (yesterdayCtl > 0 && yesterdayDaysInWeek > 0) {
                    (tssy.toFloat() / (yesterdayCtl * yesterdayDaysInWeek)) * 100f
                } else {
                    0f
                }
                val score = calculateRecoveryStateScore(
                    SD1 = sd1,
                    SD7 = sd7,
                    RHR1 = last1DayRestHR,
                    RHR7 = last7DaysRestHR,
                    HRV1 = last1DayHrv,
                    HRV7 = last7DaysHrv,
                    HRVL = hrvNormalRange.first,
                    HRVU = hrvNormalRange.last,
                    TSSY = tssy,
                    TSS1 = tss1,
                    ATL = todayProgress?.fatigue?.roundToInt() ?: 0,
                    CTL = ctl,
                    F7 = feeling7Days,
                    F42 = feeling42Days,
                    dailyScore = dailyScore,
                )
                if (score.suuntoScore > 0) {
                    RecoveryStateGraphData(
                        date = date,
                        recoveryStateData = RecoveryStateData(
                            recoveryScore = score.suuntoScore,
                            recoveryZone = score.suuntoScore.toRecoveryZone(),
                            contributors = if (includeContributors) {
                                RecoveryStateContributors(
                                    last1DaySleepDuration = last1DaySleep.getAvgLongSleepDuration(),
                                    last7DaysSleepDuration = last7DaysSleep.getAvgLongSleepDuration(),
                                    last1DaySleepDurationTotalSeconds = sd1TotalSeconds,
                                    last7DaysSleepDurationTotalSeconds = sd7TotalSeconds,
                                    last1DayRestHR = last1DayRestHR,
                                    last7DaysRestHR = last7DaysRestHR,
                                    last1DayMinHR = last1DayMinHR,
                                    last7DaysMinHR = last7DaysMinHR,
                                    last1DayHrv = last1DayHrv,
                                    last7DaysHrv = last7DaysHrv,
                                    yesterdayTSS = tssy,
                                    todayTSS = tss1,
                                    todayTSB = tsb1,
                                    last7DaysFeeling = feeling7Days,
                                    todayResources = dailyResources,
                                    hrvLowRange = hrvNormalRange.first,
                                    hrvHighRange = hrvNormalRange.last,
                                    feelingDistribution = feelingDistribution,
                                    ctlPercent = ctlPercent,
                                    yesterdayCtlPercent = yesterdayCtlPercent,
                                )
                            } else null,
                        ),
                    )
                } else {
                    null
                }
            }
        }
    }

    private fun getFeelingByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(fromDate.minusDays(41), toDate)
        .map { list ->
            list.groupBy { it.localDate }.mapValues { it.value.first() }
        }
        .catch {
            Timber.w(it, "get feeling by date error")
            emit(emptyMap())
        }

    private fun getSleepHrvListByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = fetchSleepHrvUseCase.fetchAvgHrv(
        from = fromDate,
        to = toDate,
    ).map { sleepHrvList ->
        sleepHrvList.groupBy { it.date }
    }.catch {
        Timber.w(it, "get sleep hrv by date error")
        emit(emptyMap())
    }

    private fun getSleepListFlow(fromDate: LocalDate, toDate: LocalDate): Flow<List<Sleep>> =
        sleepRepository.fetchSleeps(
            from = fromDate,
            to = toDate,
        ).catch {
            Timber.w(it, "get sleep list error")
            emit(emptyList())
        }

    private fun getTrendDataListFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = trendDataRepository.fetchTrendDataForDateRange(
        fromTimestamp = fromDate.atStartOfDay().toEpochMilli(),
        toTimestamp = toDate.atStartOfDay().toEpochMilli(),
        aggregated = false,
    ).catch {
        Timber.w(it, "get trend data list error")
        emit(emptyList())
    }

    private fun getRecoveryDataListFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<List<RecoveryData>> = fetchRecoveryDataUseCase.fetchRecoveryData(
        from = fromDate,
        to = toDate,
    ).catch {
        Timber.w(it, "get recovery data list error")
        emit(emptyList())
    }

    private fun getProgressByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = flow {
        val username = currentUserDataSource.getCurrentUser().username
        emit(
            getTrainingProgressDataUseCase.invoke(
                GetTrainingProgressDataUseCase.Params(
                    username = username,
                    firstDay = fromDate.minusDays(1),
                    lastDay = toDate,
                    addZeroValuesBeforeFirstRecordedTssDate = false,
                )
            ).groupBy { it.day }.mapValues { it.value.first() }
        )
    }.catch {
        Timber.w(it, "get progress by date error")
        emit(emptyMap())
    }

    private fun getRestHeartRateByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate,
    ) = fetchRestHeartRateUseCase.fetchRestHeartRateForDateRange(fromDate.minusDays(6), toDate)
        .map { list ->
            list.groupBy { it.localDate }.mapValues { it.value.first() }
        }
        .catch {
            Timber.w(it, "get rest heart rate by date error")
            emit(emptyMap())
        }

    private fun getDailyScore(dailyResources: Int, dailySteps: Int): Int {
        val userSettings = userSettingsDataSource.getUserSettings()
        val gender = if ("male".equals(userSettings.gender, true)) 1 else 0
        val age = calculateAge(userSettings.birthDate)
        val weightInKilogram = userSettings.weight / 1000.0
        val heightInMeter = userSettings.height / 100.0
        return calculateDailyScore(
            age = age,
            gender = gender,
            weightInKilogram = weightInKilogram,
            heightInMeter = heightInMeter,
            dailyResources = dailyResources,
            dailySteps = dailySteps,
        )
    }

    private fun Map<LocalDate, DailyFeeling>.getAvgFeelingForTimeRage(
        date: LocalDate,
        daysCount: Int,
    ): Double {
        val feelingList = (0 until daysCount).mapNotNull {
            get(date.minusDays(it.toLong()))
                .takeIf { feeling ->
                    (feeling?.averageValue ?: 0.0) > 0
                }
        }

        val avg = feelingList.flatMap { it.values }.average().takeIfNotNaN() ?: 0.0
        return (avg * 10).roundToInt() / 10.0
    }

    private fun List<Sleep>.getAvgLongSleepDuration(): Float {
        val dailySleeps =
            groupBy { it.timestamp.toLocalDate() }.filter { it.value.isNotEmpty() && it.value.any { sleep -> sleep.hasLongSleep } }
        if (dailySleeps.none()) return 0f
        val seconds = dailySleeps.values
            .sumOf { sleeps ->
                sleeps.sumOf { it.longSleep?.sleepDuration?.inWholeSeconds ?: 0L }
            }
            .toFloat()
        val avgSeconds = seconds / dailySleeps.size
        return avgSeconds
    }

    private fun List<Sleep>.getAvgSleepDuration(): Double {
        val dailySleeps =
            groupBy { it.timestamp.toLocalDate() }.filter { it.value.isNotEmpty() && it.value.any { sleep -> sleep.hasLongSleep } }
        if (dailySleeps.none()) return 0.0
        val seconds = dailySleeps.values
            .sumOf { sleeps ->
                sleeps.sumOf { it.longSleep?.sleepDuration?.inWholeSeconds ?: 0L }
            }
            .toFloat()
        val avgSeconds = seconds / dailySleeps.size
        val hours = avgSeconds / TimeUnit.HOURS.toSeconds(1)
        return (hours * 10).roundToInt() / 10.0
    }

    private fun List<Sleep>.getTotalSleepDurationInSeconds(): Long {
        if (isEmpty()) return 0L
        return sumOf { sleep ->
            val nightSleep = if (sleep.hasLongSleep) {
                sleep.longSleep?.sleepDuration?.inWholeSeconds ?: 0L
            } else 0L
            val napSleep = sleep.getMergedNap()?.duration?.inWholeSeconds ?: 0L
            nightSleep + napSleep
        }
    }

    private fun List<Sleep>.getAvgSleepDurationInSeconds(): Long {
        val dailySleeps =
            groupBy { it.timestamp.toLocalDate() }.filter { it.value.isNotEmpty() && it.value.any { sleep -> sleep.hasLongSleep } }
        if (dailySleeps.none()) return 0L
        val totalSeconds = dailySleeps.values
            .sumOf { sleeps ->
                sleeps.sumOf { sleep ->
                    val nightSleep = if (sleep.hasLongSleep) {
                        sleep.longSleep?.sleepDuration?.inWholeSeconds ?: 0L
                    } else 0L
                    val napSleep = sleep.getMergedNap()?.duration?.inWholeSeconds ?: 0L
                    nightSleep + napSleep
                }
            }
        return totalSeconds / dailySleeps.size
    }

    private fun calculateAge(birthDate: Long): Int {
        val now = LocalDate.now(ZoneId.systemDefault())
        val birthdayDate = Instant.ofEpochMilli(birthDate).atZone(ZoneId.systemDefault())
            .toLocalDate()
        var age = now.year - birthdayDate.year
        if (now.dayOfYear < birthdayDate.dayOfYear) {
            age--
        }
        return age
    }

    private fun Int.toRecoveryZone(): RecoveryZone = when (this) {
        in 80..100 -> RecoveryZone.OPTIMAL
        in 60..79 -> RecoveryZone.GOOD
        in 40..59 -> RecoveryZone.FAIR
        in 20..39 -> RecoveryZone.LIMITED
        in 0..19 -> RecoveryZone.POOR
        else -> RecoveryZone.NO_DATA
    }


    private fun getFeelingDistributionFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<Map<Int, Double>> = flow {
        val startDate = fromDate.minusDays(6)
        runSuspendCatching {
            val dailyFeelings = fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(
                startDate, toDate
            ).first()
            val rawFeelings = dailyFeelings.map { dailyFeeling -> dailyFeeling.averageValue.toInt() }
            val validFeelings = rawFeelings.filter { value -> value in 1..5 }
            val countMap = (1..5).associateWith { feeling ->
                validFeelings.count { value -> value == feeling }
            }
            val totalCount = validFeelings.size
            if (totalCount > 0) {
                countMap.mapValues { entry -> 
                    val count = entry.value
                    val percentage = count * 100.0 / totalCount
                    (percentage * 10).roundToInt() / 10.0
                }
            } else {
                (1..5).associateWith { 20.0 }
            }
        }.getOrNull().let { percentageMap ->
            emit(percentageMap ?: (1..5).associateWith { 20.0 })
            
            if (percentageMap == null) {
                Timber.w("Calculate feeling distribution error, returning default 20%% values")
            }
        }
    }

    private fun calculateMinimumDaytimeHeartRate(
        date: LocalDate,
        trendDataList: List<TrendData>,
        sleepList: List<Sleep>
    ): Int {
        val sleepTimeRanges = sleepList.mapNotNull { sleep ->
            sleep.longSleep?.let { it.fellAsleep..it.wokeUp }
        }
        
        val daytimeData = trendDataList.filter { trendData ->
            sleepTimeRanges.none { range -> trendData.timestamp in range }
        }
        
        val minHeartRates = daytimeData.mapNotNull { trendData ->
            getMinHeartRate(trendData)
        }
        
        return minHeartRates.minOrNull()?.inBpm?.roundToInt() ?: 0
    }
    
    private fun getMinHeartRate(trendData: TrendData): HeartRate? {
        val hr = trendData.hr?.takeIf { trendData.hasHr }
        val minHr = trendData.hrMin
        return if (minHr != null && hr != null) {
            minOf(minHr, hr)
        } else {
            (minHr ?: hr)
        }?.hz
    }


}
