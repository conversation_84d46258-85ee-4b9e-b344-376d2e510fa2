package com.stt.android.data.activitydata.goals

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import com.stt.android.TestOpen
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.ACTIVITY_DATA_PREFS_NAME
import com.stt.android.data.Local
import com.stt.android.data.Remote
import com.stt.android.data.source.local.activitydata.ActivityDataSharedPrefStorage
import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.Energy.Companion.joules
import com.suunto.algorithms.data.Energy.Companion.kcal
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

@Module
@InstallIn(SingletonComponent::class)
abstract class ActivityDataGoalModule {

    @Binds
    abstract fun bindActivityDataDailyRepository(
        repositoryImpl: ActivityDataGoalRepositoryImpl
    ): ActivityDataGoalRepository
}

/**
 * Repository definition to fetch and set activity data goals
 */
@TestOpen
class ActivityDataGoalRepositoryImpl
@Inject constructor(
    private val application: Application,
    @Local private val activityDataGoalLocalDataSource: ActivityDataGoalDataSource,
    @Remote private val activityDataGoalRemoteDataSource: ActivityDataGoalDataSource,
) : ActivityDataGoalRepository {

    private val activityDataSharedPreferences: SharedPreferences by lazy {
        application.getSharedPreferences(ACTIVITY_DATA_PREFS_NAME, Context.MODE_PRIVATE)
    }

    override fun fetchStepsGoal(): Flow<Int> = createGoalFlow(
        sharedPrefsKey = ActivityDataSharedPrefStorage.KEY_STEPS_GOAL_PUBLIC,
        fetchLocal = { activityDataGoalLocalDataSource.fetchStepsGoal() },
        fetchRemote = { activityDataGoalRemoteDataSource.fetchStepsGoal() },
        setLocal = { goal -> activityDataGoalLocalDataSource.setStepsGoal(goal) },
        validateAndDefault = { goal -> goal.takeIf { it >= 0 } ?: ActivityDataType.Steps().goal },
        errorMessage = "Error while fetching steps goal from watch"
    )

    override fun fetchEnergyGoal(): Flow<Energy> = createGoalFlow(
        sharedPrefsKey = ActivityDataSharedPrefStorage.KEY_ENERGY_GOAL_PUBLIC,
        fetchLocal = { activityDataGoalLocalDataSource.fetchEnergyGoal().joules },
        fetchRemote = { activityDataGoalRemoteDataSource.fetchEnergyGoal().joules },
        setLocal = { goal -> activityDataGoalLocalDataSource.setEnergyGoal(goal.inJoules.roundToInt()) },
        validateAndDefault = { goal -> goal.takeIf { it.inCal >= 0.0 } ?: ActivityDataType.Energy().goal.kcal },
        errorMessage = "Error while fetching energy goal from watch"
    )

    override fun fetchSleepGoal(): Flow<Duration> = createGoalFlow(
        sharedPrefsKey = ActivityDataSharedPrefStorage.KEY_SLEEP_GOAL_PUBLIC,
        fetchLocal = { activityDataGoalLocalDataSource.fetchSleepGoal().seconds },
        fetchRemote = { activityDataGoalRemoteDataSource.fetchSleepGoal().seconds },
        setLocal = { goal -> activityDataGoalLocalDataSource.setSleepGoal(goal.inWholeSeconds.toInt()) },
        validateAndDefault = { goal -> goal.takeIf { it >= 0.seconds } ?: ActivityDataType.SleepDuration().goal.seconds },
        errorMessage = "Error while fetching sleep goal from watch"
    )

    override fun fetchBedtimeStart(): Flow<Int> = createGoalFlow(
        sharedPrefsKey = ActivityDataSharedPrefStorage.KEY_BEDTIME_START_PUBLIC,
        fetchLocal = { activityDataGoalLocalDataSource.fetchBedtimeStart() },
        fetchRemote = { activityDataGoalRemoteDataSource.fetchBedtimeStart() },
        setLocal = { bedtimeStart -> activityDataGoalLocalDataSource.setBedtimeStart(bedtimeStart) },
        validateAndDefault = { it }, // No validation needed for bedtime
        errorMessage = "Error while fetching start bedtime from watch"
    )

    override fun fetchBedtimeEnd(): Flow<Int> = createGoalFlow(
        sharedPrefsKey = ActivityDataSharedPrefStorage.KEY_BEDTIME_END_PUBLIC,
        fetchLocal = { activityDataGoalLocalDataSource.fetchBedtimeEnd() },
        fetchRemote = { activityDataGoalRemoteDataSource.fetchBedtimeEnd() },
        setLocal = { bedtimeEnd -> activityDataGoalLocalDataSource.setBedtimeEnd(bedtimeEnd) },
        validateAndDefault = { it }, // No validation needed for bedtime
        errorMessage = "Error while fetching end bedtime from watch"
    )

    override suspend fun setStepsGoal(goal: Int) {
        activityDataGoalRemoteDataSource.setStepsGoal(goal)
        activityDataGoalLocalDataSource.setStepsGoal(goal)
    }

    override suspend fun setEnergyGoal(goal: Energy) {
        val joules = goal.inJoules.roundToInt()
        activityDataGoalRemoteDataSource.setEnergyGoal(joules)
        activityDataGoalLocalDataSource.setEnergyGoal(joules)
    }

    override suspend fun setSleepGoal(goal: Duration) {
        activityDataGoalRemoteDataSource.setSleepGoal(goal.inWholeSeconds.toInt())
        activityDataGoalLocalDataSource.setSleepGoal(goal.inWholeSeconds.toInt())
    }

    override suspend fun setBedtimes(bedtimeStart: Int, bedtimeEnd: Int) {
        activityDataGoalRemoteDataSource.setBedtimes(bedtimeStart, bedtimeEnd)
        activityDataGoalLocalDataSource.setBedtimes(bedtimeStart, bedtimeEnd)
    }

    override suspend fun fetchLocalStepsGoal(): Int {
        return activityDataGoalLocalDataSource.fetchStepsGoal()
    }

    override suspend fun fetchLocalEnergyGoal(): Energy {
        return activityDataGoalLocalDataSource.fetchEnergyGoal().joules
    }

    override suspend fun fetchLocalSleepGoal(): Duration {
        return activityDataGoalLocalDataSource.fetchSleepGoal().seconds
    }

    /**
     * Creates a reactive Flow that listens to SharedPreferences changes for a specific goal type
     */
    private fun <T> createGoalFlow(
        sharedPrefsKey: String,
        fetchLocal: suspend () -> T,
        fetchRemote: suspend () -> T,
        setLocal: suspend (T) -> Unit,
        validateAndDefault: (T) -> T,
        errorMessage: String
    ): Flow<T> = callbackFlow {
        suspend fun emitGoal() {
            val localGoal = runSuspendCatching { fetchLocal() }.getOrElse {
                return
            }
            val validatedGoal = validateAndDefault(localGoal)
            trySend(validatedGoal)
        }

        val sharedPrefsListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
            if (key == sharedPrefsKey) {
                launch { emitGoal() }
            }
        }
        activityDataSharedPreferences.registerOnSharedPreferenceChangeListener(sharedPrefsListener)

        emitGoal()

        launch {
            runSuspendCatching {
                val remoteGoal = fetchRemote()
                // The SharedPreferences listener will automatically emit the updated value
                setLocal(remoteGoal)
            }.onFailure { e ->
                Timber.w(e, errorMessage)
            }
        }

        awaitClose {
            activityDataSharedPreferences.unregisterOnSharedPreferenceChangeListener(sharedPrefsListener)
        }
    }.distinctUntilChanged()
}
