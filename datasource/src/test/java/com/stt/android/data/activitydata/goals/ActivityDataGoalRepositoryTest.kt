package com.stt.android.data.activitydata.goals

import android.app.Application
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.suunto.algorithms.data.Energy.Companion.joules
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.time.Duration.Companion.seconds

@RunWith(MockitoJUnitRunner::class)
class ActivityDataGoalRepositoryTest {
    @Mock
    private lateinit var application: Application

    @Mock
    private lateinit var activityDataGoalRemoteDataSource: ActivityDataGoalRemoteDataSource

    @Mock
    private lateinit var activityDataGoalLocalDataSource: ActivityDataGoalLocalDataSource

    private lateinit var activityDataGoalDataRepository: ActivityDataGoalRepository

    @Before
    fun setup() {
        activityDataGoalDataRepository = ActivityDataGoalRepositoryImpl(application, activityDataGoalLocalDataSource, activityDataGoalRemoteDataSource)
    }

    @Test
    fun `get steps goal should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchStepsGoal())
            .thenReturn(10000)
        whenever(activityDataGoalLocalDataSource.fetchStepsGoal())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchStepsGoal().collect()
        verify(activityDataGoalRemoteDataSource).fetchStepsGoal()
        verify(activityDataGoalLocalDataSource, times(1)).fetchStepsGoal()
    }

    @Test
    fun `get steps goal should access local in case error on remote`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchStepsGoal())
            .thenThrow(RuntimeException())
        whenever(activityDataGoalLocalDataSource.fetchStepsGoal())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchStepsGoal().collect()
        verify(activityDataGoalRemoteDataSource).fetchStepsGoal()
        verify(activityDataGoalLocalDataSource, times(2)).fetchStepsGoal()
    }

    @Test
    fun `get energy goal should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchEnergyGoal())
            .thenReturn(10000)
        whenever(activityDataGoalLocalDataSource.fetchEnergyGoal())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchEnergyGoal()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchEnergyGoal()
        verify(activityDataGoalLocalDataSource, times(1)).fetchEnergyGoal()
    }

    @Test
    fun `get energy goal should access local in case error on remote`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchEnergyGoal())
            .thenThrow(RuntimeException())
        whenever(activityDataGoalLocalDataSource.fetchEnergyGoal())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchEnergyGoal()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchEnergyGoal()
        verify(activityDataGoalLocalDataSource, times(2)).fetchEnergyGoal()
    }

    @Test
    fun `get sleep goal should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchSleepGoal())
            .thenReturn(10000)
        whenever(activityDataGoalLocalDataSource.fetchSleepGoal())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchSleepGoal()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchSleepGoal()
        verify(activityDataGoalLocalDataSource, times(1)).fetchSleepGoal()
    }

    @Test
    fun `get sleep goal should access local in case error on remote`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchSleepGoal())
            .thenThrow(RuntimeException())
        whenever(activityDataGoalLocalDataSource.fetchSleepGoal())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchSleepGoal()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchSleepGoal()
        verify(activityDataGoalLocalDataSource, times(2)).fetchSleepGoal()
    }

    @Test
    fun `get bedtime start should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchBedtimeStart())
            .thenReturn(10000)
        whenever(activityDataGoalLocalDataSource.fetchBedtimeStart())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchBedtimeStart()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchBedtimeStart()
        verify(activityDataGoalLocalDataSource, times(1)).fetchBedtimeStart()
    }

    @Test
    fun `get bedtime start should access local in case error on remote`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchBedtimeStart())
            .thenThrow(RuntimeException())
        whenever(activityDataGoalLocalDataSource.fetchBedtimeStart())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchBedtimeStart()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchBedtimeStart()
        verify(activityDataGoalLocalDataSource, times(2)).fetchBedtimeStart()
    }

    @Test
    fun `get bedtime end should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchBedtimeEnd())
            .thenReturn(20000)
        whenever(activityDataGoalLocalDataSource.fetchBedtimeEnd())
            .thenReturn(20000)
        // verify
        activityDataGoalDataRepository.fetchBedtimeEnd()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchBedtimeEnd()
        verify(activityDataGoalLocalDataSource, times(1)).fetchBedtimeEnd()
    }

    @Test
    fun `get bedtime end should access local in case error on remote`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.fetchBedtimeEnd())
            .thenThrow(RuntimeException())
        whenever(activityDataGoalLocalDataSource.fetchBedtimeEnd())
            .thenReturn(10000)
        // verify
        activityDataGoalDataRepository.fetchBedtimeEnd()
            .collect()
        verify(activityDataGoalRemoteDataSource).fetchBedtimeEnd()
        verify(activityDataGoalLocalDataSource, times(2)).fetchBedtimeEnd()
    }

    @Test
    fun `set steps goal should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.setStepsGoal(any()))
            .doReturn(Unit)
        whenever(activityDataGoalLocalDataSource.setStepsGoal(any()))
            .doReturn(Unit)
        // verify
        activityDataGoalDataRepository.setStepsGoal(10000)
        verify(activityDataGoalRemoteDataSource).setStepsGoal(any())
        verify(activityDataGoalLocalDataSource).setStepsGoal(any())
    }

    @Test
    fun `set energy goal should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.setEnergyGoal(any()))
            .thenReturn(Unit)
        whenever(activityDataGoalLocalDataSource.setEnergyGoal(any()))
            .thenReturn(Unit)
        // verify
        activityDataGoalDataRepository.setEnergyGoal(10000.joules)
        verify(activityDataGoalRemoteDataSource).setEnergyGoal(any())
        verify(activityDataGoalLocalDataSource).setEnergyGoal(any())
    }

    @Test
    fun `set sleep goal should access local and remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.setSleepGoal(any()))
            .thenReturn(Unit)
        whenever(activityDataGoalLocalDataSource.setSleepGoal(any()))
            .thenReturn(Unit)
        // verify
        activityDataGoalDataRepository.setSleepGoal(10000.seconds)
        verify(activityDataGoalRemoteDataSource).setSleepGoal(any())
        verify(activityDataGoalLocalDataSource).setSleepGoal(any())
    }

    @Test
    fun `set sleep goal with bedtime should access only remote data source`() = runTest {
        // prepare
        whenever(activityDataGoalRemoteDataSource.setBedtimes(any(), any())).thenReturn(Unit)
        whenever(activityDataGoalLocalDataSource.setBedtimes(any(), any())).thenReturn(Unit)
        // verify
        activityDataGoalDataRepository.setBedtimes(10000, 20000)
        verify(activityDataGoalRemoteDataSource).setBedtimes(any(), any())
        verify(activityDataGoalLocalDataSource).setBedtimes(any(), any())
    }
}
