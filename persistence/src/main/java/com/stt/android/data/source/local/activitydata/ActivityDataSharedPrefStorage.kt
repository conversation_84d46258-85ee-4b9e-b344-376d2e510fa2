package com.stt.android.data.source.local.activitydata

import android.app.Application
import android.content.Context
import androidx.core.content.edit
import com.stt.android.data.ACTIVITY_DATA_PREFS_NAME
import com.suunto.algorithms.data.Energy.Companion.kcal
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt

class ActivityDataSharedPrefStorage
@Inject constructor(
    val application: Application
) {
    fun fetchStepsValue(): Int = fetchFromSharedPrefs(KEY_STEPS_VALUE, DEFAULT_STEPS_VALUE)

    fun getDefaultStepsValue(): Int = DEFAULT_STEPS_VALUE

    fun fetchEnergyValue(): Int = fetchFromSharedPrefs(KEY_ENERGY_VALUE, DEFAULT_ENERGY_VALUE)

    fun getDefaultEnergyValue(): Int = DEFAULT_ENERGY_VALUE

    fun fetchBalanceValue(): Float = fetchFromSharedPrefs(KEY_BALANCE_VALUE, DEFAULT_BALANCE_VALUE)

    fun getDefaultBalanceValue(): Float = DEFAULT_BALANCE_VALUE

    fun fetchStressStateValue(): Int =
        fetchFromSharedPrefs(KEY_STRESS_STATE_VALUE, DEFAULT_STRESS_STATE_VALUE)

    fun getDefaultStressStateValue(): Int = DEFAULT_STRESS_STATE_VALUE

    fun fetchMetabolicEnergyValue(): Int =
        fetchFromSharedPrefs(KEY_METABOLIC_ENERGY_VALUE, DEFAULT_METABOLIC_ENERGY_VALUE)

    fun fetchSleepTrackingMode(): Int =
        getSharedPrefs().getInt(KEY_SLEEP_TRACKING_MODE, DEFAULT_SLEEP_TRACKING_MODE)

    fun fetchStepsGoal(): Int = fetchFromSharedPrefs(KEY_STEPS_GOAL, DEFAULT_STEPS_GOAL)

    fun fetchEnergyGoal(): Int = fetchFromSharedPrefs(KEY_ENERGY_GOAL, DEFAULT_ENERGY_IN_JOULES_GOAL)

    fun fetchSleepGoal(): Int = fetchFromSharedPrefs(KEY_SLEEP_GOAL, DEFAULT_SLEEP_GOAL)

    fun fetchBedtimeStart(): Int = fetchFromSharedPrefs(KEY_BEDTIME_START, DEFAULT_BEDTIME_START_VALUE)

    fun fetchBedtimeEnd(): Int = fetchFromSharedPrefs(KEY_BEDTIME_END, DEFAULT_BEDTIME_END_VALUE)

    fun fetchStepsSavingTimestamp(timestamp: Long): Long = getSharedPrefs()
        .getLong(KEY_STEPS_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp)

    fun fetchEnergySavingTimestamp(timestamp: Long): Long = getSharedPrefs()
        .getLong(KEY_ENERGY_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp)

    fun fetchBalanceSavingTimestamp(timestamp: Long): Long = getSharedPrefs()
        .getLong(KEY_BALANCE_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp)

    fun fetchStressStateSavingTimestamp(timestamp: Long): Long = getSharedPrefs()
        .getLong(KEY_STRESS_STATE_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp)

    fun saveStepsValue(value: Int) {
        saveToSharedPrefs(value, KEY_STEPS_VALUE)
    }

    fun saveEnergyValue(value: Int) {
        saveToSharedPrefs(value, KEY_ENERGY_VALUE)
    }

    fun saveBalanceValue(value: Float) {
        saveToSharedPrefs(value, KEY_BALANCE_VALUE)
    }

    fun resetBalanceValue() {
        saveToSharedPrefs(DEFAULT_BALANCE_VALUE, KEY_BALANCE_VALUE)
    }

    fun saveStressStateValue(value: Int) {
        saveToSharedPrefs(value, KEY_STRESS_STATE_VALUE)
    }

    fun resetStressStateValue() {
        saveToSharedPrefs(DEFAULT_STRESS_STATE_VALUE, KEY_STRESS_STATE_VALUE)
    }

    fun resetStepsValue() {
        saveToSharedPrefs(DEFAULT_STEPS_VALUE, KEY_STEPS_VALUE)
    }

    fun resetEnergyValue() {
        saveToSharedPrefs(DEFAULT_ENERGY_VALUE, KEY_ENERGY_VALUE)
    }

    fun saveMetabolicEnergyValue(value: Int) {
        saveToSharedPrefs(value, KEY_METABOLIC_ENERGY_VALUE)
    }

    fun saveStepsGoal(goal: Int) {
        saveToSharedPrefs(goal, KEY_STEPS_GOAL)
    }

    fun saveEnergyGoal(goal: Int) {
        saveToSharedPrefs(goal, KEY_ENERGY_GOAL)
    }

    fun saveSleepGoal(goal: Int) {
        saveToSharedPrefs(goal, KEY_SLEEP_GOAL)
    }

    fun saveSleepTrackingMode(sleepTrackingMode: Int) {
        getSharedPrefs().edit {
            putInt(KEY_SLEEP_TRACKING_MODE, sleepTrackingMode)
        }
    }

    fun saveBedtimeStart(bedtimeStart: Int) {
        getSharedPrefs().edit {
            putInt(KEY_BEDTIME_START, bedtimeStart)
        }
    }

    fun saveBedtimeEnd(bedtimeEnd: Int) {
        getSharedPrefs().edit {
            putInt(KEY_BEDTIME_END, bedtimeEnd)
        }
    }

    fun saveStepsValueLocalSavingTimestamp(timestamp: Long) {
        Timber.d("Saving last steps value fetching to shared preferences: $timestamp")
        getSharedPrefs().edit { putLong(KEY_STEPS_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp) }
    }

    fun saveEnergyValueLocalSavingTimestamp(timestamp: Long) {
        Timber.d("Saving last energy value fetching to shared preferences: $timestamp")
        getSharedPrefs().edit { putLong(KEY_ENERGY_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp) }
    }

    fun saveBalanceValueLocalSavingTimestamp(timestamp: Long) {
        Timber.d("Saving last balance value fetching to shared preferences: $timestamp")
        getSharedPrefs().edit { putLong(KEY_BALANCE_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp) }
    }

    fun saveStressStateValueLocalSavingTimestamp(timestamp: Long) {
        Timber.d("Saving last stress state value fetching to shared preferences: $timestamp")
        getSharedPrefs().edit { putLong(KEY_STRESS_STATE_VALUE_LOCAL_SAVE_TIMESTAMP, timestamp) }
    }

    fun saveBedtimes(bedtimeStart: Int, bedtimeEnd: Int) {
        getSharedPrefs().edit {
            putInt(KEY_BEDTIME_START, bedtimeStart).apply()
            putInt(KEY_BEDTIME_END, bedtimeEnd).apply()
        }
    }

    private fun <T : Number> saveToSharedPrefs(value: T, key: String) {
        Timber.d("Saving $key to shared preferences: $value")
        getSharedPrefs().edit {
            when (value) {
                is Int -> putInt(key, value)
                is Float -> putFloat(key, value)
                is Long -> putLong(key, value)
                else -> throw IllegalArgumentException("saveToSharedPrefs supports only Int, Float and Long")
            }
        }
    }

    private fun <T : Number> fetchFromSharedPrefs(key: String, defaultValue: T): T {
        val value = when (defaultValue) {
            is Int -> getSharedPrefs().getInt(key, defaultValue)
            is Float -> getSharedPrefs().getFloat(key, defaultValue)
            is Long -> getSharedPrefs().getLong(key, defaultValue)
            else -> throw IllegalArgumentException("fetchFromSharedPrefs supports only Int, Float and Long")
        }
        Timber.d("Fetching $key from shared preferences: $value")
        return value as T
    }

    private fun getSharedPrefs() = application.getSharedPreferences(ACTIVITY_DATA_PREFS_NAME, Context.MODE_PRIVATE)

    companion object {
        private const val KEY_SLEEP_TRACKING_MODE = "key_sleep_tracking_mode"
        private const val KEY_BEDTIME_START = "key_bedtimes_start"
        private const val KEY_BEDTIME_END = "key_bedtimes_end"
        private const val KEY_SLEEP_GOAL = "key_sleep_goal"
        private const val KEY_STEPS_VALUE_LOCAL_SAVE_TIMESTAMP = "key_steps_value_local_save_time_stamp"
        private const val KEY_ENERGY_VALUE_LOCAL_SAVE_TIMESTAMP = "key_energy_valuea_local_save_time_stamp"
        private const val KEY_BALANCE_VALUE_LOCAL_SAVE_TIMESTAMP = "key_balance_value_local_save_time_stamp"
        private const val KEY_STRESS_STATE_VALUE_LOCAL_SAVE_TIMESTAMP = "key_stress_state_value_local_save_time_stamp"
        private const val DEFAULT_SLEEP_TRACKING_MODE = 0 // OFF
        private const val KEY_STEPS_VALUE = "key_steps_value"
        private const val KEY_ENERGY_VALUE = "key_energy_value"

        @Suppress("unused")
        @Deprecated("Old balance value stored 0..100")
        private const val KEY_BALANCE_VALUE_OLD = "key_balance_value"

        // new balance value is stored as float 0..1
        private const val KEY_BALANCE_VALUE = "key_balance_value_v2"
        private const val KEY_STRESS_STATE_VALUE = "key_stress_state_value"
        private const val KEY_METABOLIC_ENERGY_VALUE = "key_metabolic_energy_value"
        private const val KEY_STEPS_GOAL = "key_steps_goal"
        private const val KEY_ENERGY_GOAL = "key_energy_goal"
        private const val DEFAULT_STEPS_GOAL = 10000
        private val DEFAULT_ENERGY_IN_JOULES_GOAL = 500.kcal.inJoules.roundToInt()
        private val DEFAULT_SLEEP_GOAL = TimeUnit.HOURS.toSeconds(8).toInt()
        private const val DEFAULT_STEPS_VALUE = 0
        private const val DEFAULT_ENERGY_VALUE = 0
        private const val DEFAULT_BALANCE_VALUE = -1f
        private const val DEFAULT_STRESS_STATE_VALUE = 0
        private val DEFAULT_BEDTIME_START_VALUE = TimeUnit.HOURS.toSeconds(22).toInt()
        private val DEFAULT_BEDTIME_END_VALUE = TimeUnit.HOURS.toSeconds(8).toInt()
        const val DEFAULT_METABOLIC_ENERGY_VALUE = 7916128

        // Expose keys for SharedPreferences listeners
        const val KEY_STEPS_GOAL_PUBLIC = KEY_STEPS_GOAL
        const val KEY_ENERGY_GOAL_PUBLIC = KEY_ENERGY_GOAL
        const val KEY_SLEEP_GOAL_PUBLIC = KEY_SLEEP_GOAL
        const val KEY_BEDTIME_START_PUBLIC = KEY_BEDTIME_START
        const val KEY_BEDTIME_END_PUBLIC = KEY_BEDTIME_END
    }
}
