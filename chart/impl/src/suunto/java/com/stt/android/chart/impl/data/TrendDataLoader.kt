package com.stt.android.chart.impl.data

import android.content.Context
import androidx.annotation.ColorInt
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.R
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.model.minutesSinceEpoch
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.trenddata.FetchTrendDataUseCase
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.utils.atEndOfDay
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import kotlin.math.ceil
import kotlin.math.max

internal abstract class TrendDataLoader(
    private val appContext: Context,
    private val userSettingsController: UserSettingsController,
    private val fetchTrendDataUseCase: FetchTrendDataUseCase,
) {
    protected fun loadTrendData(
        chartGranularity: ChartGranularity,
        dateRange: ClosedRange<LocalDate>,
    ): Flow<List<TrendData>> = fetchTrendDataUseCase.fetchTrendData(
        from = dateRange.start,
        to = dateRange.endInclusive,
        aggregated = chartGranularity != ChartGranularity.DAILY,
    )
        .onEmpty { emit(emptyList()) }
        .map { it.sortedBy(TrendData::timestamp) }

    protected fun createComparisonDataSeries(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        currentDateRange: ClosedRange<LocalDate>,
        comparisonDateRange: ClosedRange<LocalDate>?,
        trendDataList: List<TrendData>?,
    ): ChartData.Series? {
        if (comparisonDateRange == null || trendDataList.isNullOrEmpty()) {
            return null
        }

        val created = createDataSeries(
            chartStyle = chartStyle,
            chartGranularity = chartGranularity,
            dateRange = comparisonDateRange,
            trendDataList = trendDataList,
            todayValue = 0.0F,
            goal = null,
            color = appContext.getColor(R.color.medium_grey),
            lineChartConfig = LineChartConfig.noFill(),
        )

        // Align the x so that they can overlap with the "current" entries
        val xOffset = when (chartGranularity) {
            ChartGranularity.DAILY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIXTY_DAYS,
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS,
            ChartGranularity.EIGHT_YEARS -> TODO()

            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY -> ChronoUnit.DAYS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )

            ChartGranularity.YEARLY -> ChronoUnit.MONTHS.between(
                comparisonDateRange.start,
                currentDateRange.start
            )
        }
        val updatedEntries = created.entries
            .map { entry ->
                entry.copy(
                    x = entry.x + xOffset
                )
            }.toImmutableList()
        return created.copy(
            axisRange = if (updatedEntries.isNotEmpty()) {
                created.axisRange.copy(
                    minX = updatedEntries.minOf(ChartData.Entry::x).toDouble(),
                    maxX = updatedEntries.maxOf(ChartData.Entry::x).toDouble(),
                )
            } else {
                created.axisRange
            },
            entries = updatedEntries,
        )
    }

    protected fun createDataSeries(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        dateRange: ClosedRange<LocalDate>,
        trendDataList: List<TrendData>,
        todayValue: Float,
        goal: Number?,
        @ColorInt color: Int,
        lineChartConfig: LineChartConfig = LineChartConfig(),
    ): ChartData.Series {
        val minX: Long
        val maxX: Long
        var maxY = goal?.toFloat() ?: 0.0F
        var totalValue = 0F
        var totalDays = 0
        val entries: List<ChartData.Entry> = when (chartGranularity) {
            ChartGranularity.DAILY -> buildList {
                trendDataList.forEach { trendData ->
                    // For daily chart, no need to check today's value for data in chart.
                    val trendValue = getValue(trendData).toFloat()
                    totalValue += trendValue

                    val y = when (chartStyle) {
                        ChartStyle.SINGLE -> trendValue
                        ChartStyle.CUMULATIVE -> totalValue
                    }
                    maxY = max(maxY, y)

                    if (chartStyle != ChartStyle.SINGLE || y > 0) {
                        val entry = ChartData.Entry(
                            x = trendData.timeISO8601.minutesSinceEpoch,
                            y = y,
                        )
                        add(entry)
                    }
                }

                // For today's data, if not all trend data has been synced, we need to rely on
                // steps from ActivityDataDailyRepository.
                if (dateRange.start == LocalDate.now()) {
                    totalValue = max(totalValue, todayValue)
                }

                totalDays = 1
                minX = dateRange.start.atStartOfDay().minutesSinceEpoch
                maxX = dateRange.endInclusive.atEndOfDay().minutesSinceEpoch - 10L
            }

            ChartGranularity.WEEKLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.MONTHLY,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS -> buildList {
                trendDataList.forEach { trendData ->
                    val trendValue = getValueFromTrend(
                        trendData = trendData,
                        todayValue = todayValue,
                    )
                    if (trendValue > 0) {
                        totalDays++
                        totalValue += trendValue
                    }

                    val y = when (chartStyle) {
                        ChartStyle.SINGLE -> trendValue
                        ChartStyle.CUMULATIVE -> totalValue
                    }
                    maxY = max(maxY, y)

                    if (chartStyle != ChartStyle.SINGLE || y > 0) {
                        val entry = ChartData.Entry(
                            x = trendData.timeISO8601.toLocalDate().toEpochDay(),
                            y = y,
                        )
                        add(entry)
                    }
                }

                minX = dateRange.start.toEpochDay()
                maxX = dateRange.endInclusive.toEpochDay()
            }

            ChartGranularity.SIX_MONTHS,
            ChartGranularity.YEARLY,
            ChartGranularity.EIGHT_YEARS -> buildList {
                trendDataList.groupBy(chartGranularity)
                    .forEach { (x, trendDataList) ->
                        val previousTotalValue = totalValue
                        val previousTotalDays = totalDays
                        trendDataList.forEach { trendData ->
                            val trendValue = getValueFromTrend(
                                trendData = trendData,
                                todayValue = todayValue,
                            )
                            if (trendValue > 0) {
                                totalDays++
                                totalValue += trendValue
                            }
                        }

                        val y = when (chartStyle) {
                            ChartStyle.SINGLE -> {
                                val value = totalValue - previousTotalValue
                                val days = totalDays - previousTotalDays
                                if (value == 0F || days == 0) {
                                    0.0F
                                } else {
                                    value / days
                                }
                            }

                            ChartStyle.CUMULATIVE -> totalValue
                        }
                        maxY = max(maxY, y)

                        if (chartStyle != ChartStyle.SINGLE || y > 0) {
                            val entry = ChartData.Entry(
                                x = x,
                                y = y,
                            )
                            add(entry)
                        }
                    }

                minX = dateRange.start.toX(chartGranularity)
                maxX = dateRange.endInclusive.toX(chartGranularity)
            }
            ChartGranularity.SIXTY_DAYS,
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS ->  throw IllegalArgumentException("this $chartGranularity is not supported")
        }

        val factor = when {
            maxY < 150.0F -> 15.0F
            maxY < 300.0F -> 30.0F
            maxY < 6000.0F -> 300.0F
            maxY < 90000.0F -> 3000.0F
            else -> 30000.0F
        }
        maxY = max(ceil(maxY / factor) * factor, 30.0F)

        return ChartData.Series(
            chartType = when (chartStyle) {
                ChartStyle.SINGLE -> ChartType.BAR
                ChartStyle.CUMULATIVE -> ChartType.LINE
            },
            color = color,
            axisRange = ChartData.AxisRange(
                minX = minX.toDouble(),
                maxX = maxX.toDouble(),
                minY = 0.0,
                maxY = maxY.toDouble(),
            ),
            entries = entries.toImmutableList(),
            value = getFormatedValue(
                value = if (totalDays == 0) 0F else totalValue / totalDays,
            ),
            candlestickEntries = persistentListOf(),
            lineConfig = lineChartConfig,
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }

    private fun List<TrendData>.groupBy(
        chartGranularity: ChartGranularity,
    ): Map<Long, List<TrendData>> = when (chartGranularity) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.MONTHLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS -> throw IllegalArgumentException("Grouping not supported for granularity '$chartGranularity'")

        ChartGranularity.SIX_MONTHS -> {
            val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
            groupBy { trendData ->
                trendData.timeISO8601
                    .toLocalDate()
                    .with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
                    .toEpochDay()
            }
        }

        ChartGranularity.YEARLY -> groupBy { trendData ->
            trendData.timeISO8601
                .toLocalDate()
                .epochMonth
                .toLong()
        }

        ChartGranularity.EIGHT_YEARS -> groupBy { trendData ->
            trendData.timeISO8601
                .year
                .toLong()
        }
    }

    abstract fun getFormatedValue(value: Float): AnnotatedString

    private fun getValueFromTrend(
        trendData: TrendData,
        todayValue: Float,
    ): Float {
        val originalValue = getValue(trendData).toFloat()
        val epochDayOfToday = LocalDate.now().toEpochDay()
        val epochDay = trendData.timeISO8601.toLocalDate().toEpochDay()
        return if (epochDayOfToday == epochDay) {
            // For today's data, if not all trend data has been synced, we need to rely on
            // steps from ActivityDataDailyRepository.
            max(todayValue, originalValue)
        } else {
            originalValue
        }
    }

    abstract fun getValue(trendData: TrendData): Number

    private companion object {
        fun LocalDate.toX(chartGranularity: ChartGranularity): Long = when (chartGranularity) {
            ChartGranularity.DAILY,
            ChartGranularity.SIXTY_DAYS,
            ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> throw IllegalArgumentException("Not supported for granularity '$chartGranularity'")
            ChartGranularity.WEEKLY,
            ChartGranularity.MONTHLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS -> toEpochDay()

            ChartGranularity.YEARLY -> epochMonth.toLong()
            ChartGranularity.EIGHT_YEARS -> year.toLong()
        }
    }
}
