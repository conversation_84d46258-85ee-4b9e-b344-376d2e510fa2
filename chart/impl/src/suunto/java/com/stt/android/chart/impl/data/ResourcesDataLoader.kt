package com.stt.android.chart.impl.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import com.stt.android.R as BaseR
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.model.minutesSinceEpoch
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.data.TimeUtils
import com.stt.android.domain.activitydata.dailyvalues.StressState
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.recovery.RecoveryDataRepository
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyRecoveryDataUseCase
import com.stt.android.utils.toEpochMilli
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import java.time.LocalTime
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.chart.impl.screen.CurrentValueData
import kotlin.time.DurationUnit
import kotlin.time.toDuration
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.impl.usecases.ChartHighlightDateFormatterUseCase
import com.stt.android.utils.atEndOfDay
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentMapOf
import com.stt.android.core.R as CR
import com.stt.android.ui.utils.TextFormatter
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import kotlin.math.ceil
import kotlin.time.Duration.Companion.minutes
import com.stt.android.controllers.UserSettingsController
import kotlinx.collections.immutable.toImmutableMap

internal open class ResourcesDataLoader @Inject constructor(
    private val context: Context,
    private val recoveryDataRepository: RecoveryDataRepository,
    private val fetchDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase,
    private val userSettingsController: UserSettingsController,
    private val chartHighlightDateFormatter: ChartHighlightDateFormatterUseCase,
) {
    companion object {
        private const val MIN_Y = 0.0
        private const val DEFAULT_MAX_Y = 120.0
        private const val RECOVERY_DATA_FREQUENCY_SECONDS = 1800L
        private const val NO_DATA_VALUE = -1f
    }
    
    private fun adjustMaxY(maxY: Double): Double {
        if (maxY <= 0.0) {
            return DEFAULT_MAX_Y
        }
        val adjustedMaxY = ceil(maxY / 30.0) * 30.0
        return adjustedMaxY
    }
    
    fun loadGoalData(): GoalViewData = GoalViewData.None

    fun loadGoalEditorData(): GoalEditorViewData = GoalEditorViewData.None
    
    fun loadChartData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate
    ): Flow<ChartData> {
        return when (chartGranularity) {
            ChartGranularity.DAILY -> {
                combine(
                    recoveryDataRepository.fetchRecoveryDataForDateRange(
                        from.atStartOfDay().toEpochMilli(),
                        to.atTime(LocalTime.MAX).toEpochMilli()
                    ).onEmpty { emit(emptyList()) },
                    fetchDailyRecoveryDataUseCase.fetchCurrentBalance(NO_DATA_VALUE)
                ) { recoveryData, currentBalance ->
                    createDailyStressStateChart(from, to, recoveryData, currentBalance)
                }
            }
            else -> {
                recoveryDataRepository.fetchRecoveryDataForDateRange(
                    from.atStartOfDay().toEpochMilli(),
                    to.atTime(LocalTime.MAX).toEpochMilli()
                )
                .onEmpty { emit(emptyList()) }
                .map { recoveryData ->
                    when (chartGranularity) {
                        ChartGranularity.WEEKLY,
                        ChartGranularity.SEVEN_DAYS,
                        ChartGranularity.THIRTY_DAYS,
                        ChartGranularity.MONTHLY -> createDailyAggregatedChartData(chartGranularity, from, to, recoveryData)
                        ChartGranularity.SIX_MONTHS -> createWeeklyAggregatedChartData(chartGranularity, from, to, recoveryData)
                        ChartGranularity.YEARLY -> createMonthlyAggregatedChartData(chartGranularity, from, to, recoveryData)
                        ChartGranularity.SIX_WEEKS,
                        ChartGranularity.SIXTY_DAYS,
                        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
                        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
                        ChartGranularity.EIGHT_YEARS -> {
                            throw IllegalArgumentException("This loader does not support ChartGranularity.$chartGranularity")
                        }
                        else -> throw IllegalArgumentException("This loader does not support ChartGranularity.$chartGranularity")
                    }
                }
            }
        }
    }
    
    private fun createDailyAggregatedChartData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        recoveryData: List<RecoveryData>
    ): ChartData {
        val dailyEntries = mutableListOf<ChartData.Entry>()
        val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
        val dailyAverages = mutableListOf<Float>()
        
        if (recoveryData.isNotEmpty()) {
            val sortedRecoveryData = recoveryData.sortedBy { it.timestamp }
            
            val dailyRecoveryData = sortedRecoveryData.groupBy { 
                TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate() 
            }
            
            for (date in from.daysUntil(to.plusDays(1))) {
                val dayData = dailyRecoveryData[date]
                
                if (!dayData.isNullOrEmpty()) {
                    val balances = dayData.map { it.balance * 100 } 
                    
                    val avg = balances.average().toFloat()
                    val min = balances.minOrNull() ?: 0f
                    val max = balances.maxOrNull() ?: 0f
                    
                    dailyAverages.add(avg)
                    
                    dailyEntries.add(
                        ChartData.Entry(
                            x = date.toEpochDay(),
                            y = avg 
                        )
                    )
                    
                    candlestickEntries.add(
                        ChartData.CandlestickEntry(
                            x = date.toEpochDay(),
                            open = min,
                            close = max,
                            low = min,
                            high = max
                        )
                    )
                }
            }
        }
        
        val averageBalance = if (dailyAverages.isNotEmpty()) {
            dailyAverages.average().toFloat() / 100
        } else {
            NO_DATA_VALUE
        }
        
        val statesDuration = calculateStressStatesDuration(recoveryData)
        
        val minX = from.toEpochDay().toDouble()
        val maxX = to.toEpochDay().toDouble()
        
        val dataMaxY = if (candlestickEntries.isNotEmpty()) {
            candlestickEntries.maxOfOrNull { it.high.toDouble() } ?: 0.0
        } else if (dailyAverages.isNotEmpty()) {
            dailyAverages.maxOfOrNull { it.toDouble() } ?: 0.0
        } else {
            0.0
        }
        val adjustedMaxY = adjustMaxY(dataMaxY)

        val value = createBalanceAnnotatedString(averageBalance)

        val mainSeries = createSeries(
            chartType = ChartType.CANDLESTICK,
            color = context.getColor(BaseR.color.dashboard_widget_hrv),
            axisRange = ChartData.AxisRange(minX, maxX, MIN_Y, adjustedMaxY),
            entries = dailyEntries.toImmutableList(),
            value = value,
            candlestickEntries = candlestickEntries.toImmutableList()
        )
        
        return ChartData(
            chartGranularity = chartGranularity,
            series = persistentListOf(mainSeries),
            highlightEnabled = true,
            goal = null,
            currentValues = createCurrentValuesList(chartGranularity, statesDuration, recoveryData),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            chartContent = ChartContent.RESOURCES,
            highlightDecorationLines = persistentMapOf(),
            colorIndicator = null,
        )
    }
    
    private fun createDailyStressStateChart(
        from: LocalDate,
        to: LocalDate,
        recoveryData: List<RecoveryData>,
        currentBalance: Float
    ): ChartData {
        val seriesList = mutableListOf<ChartData.Series>()
        
        val targetData = recoveryData
            .filter { TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate() == from }
            .sortedBy { it.timestamp }

        val minX = from.atStartOfDay().minutesSinceEpoch
        val maxX = to.atEndOfDay().minutesSinceEpoch - 10L
        
        val dataMaxY = if (targetData.isNotEmpty()) {
            targetData.maxOfOrNull { (it.balance * 100).toDouble() } ?: 0.0
        } else {
            0.0
        }
        val adjustedMaxY = adjustMaxY(dataMaxY)
        
        val axisRange = ChartData.AxisRange(minX.toDouble(), maxX.toDouble(), MIN_Y, adjustedMaxY)
        
        val stressStateColors = mapOf(
            StressState.ACTIVE to BaseR.color.dashboard_widget_steps,
            StressState.PASSIVE to BaseR.color.dive_set_active_mode_button_border_color,
            StressState.STRESSFUL to BaseR.color.second_indicator_color,
            StressState.RELAXING to BaseR.color.dashboard_widget_hrv
        )
        
        val finalBalance = calculateFinalBalance(from, currentBalance, targetData)
        val lastBalanceValue = createBalanceAnnotatedString(finalBalance)
        
        val recoveryStateMap = mutableMapOf<Long, StressState>()
        
        if (targetData.isEmpty()) {
            seriesList.add(createSeries(
                chartType = ChartType.BAR,
                color = context.getColor(BaseR.color.dashboard_widget_steps),
                axisRange = axisRange,
                entries = persistentListOf(),
                value = lastBalanceValue
            ))
        } else {
            var currentStressState: StressState? = null
            var currentEntries = mutableListOf<ChartData.Entry>()
            
            targetData.forEach { data ->
                val time = TimeUtils.epochToLocalZonedDateTime(data.timestamp)
                val x = time.minutesSinceEpoch
                val entry = ChartData.Entry(
                    x = x,
                    y = data.balance * 100 
                )
                
                recoveryStateMap[x] = data.stressState
                
                if (data.stressState != currentStressState) {
                    if (currentEntries.isNotEmpty()) {
                        seriesList.add(createSeries(
                            chartType = ChartType.BAR,
                            color = context.getColor(stressStateColors[currentStressState] ?: BaseR.color.dashboard_widget_steps),
                            axisRange = axisRange,
                            entries = currentEntries.toImmutableList(),
                            value = lastBalanceValue
                        ))
                    }
                    
                    currentStressState = data.stressState
                    currentEntries = mutableListOf()
                }
                
                currentEntries.add(entry)
            }
            
            if (currentEntries.isNotEmpty()) {
                seriesList.add(createSeries(
                    chartType = ChartType.BAR,
                    color = context.getColor(stressStateColors[currentStressState] ?: BaseR.color.dashboard_widget_steps),
                    axisRange = axisRange,
                    entries = currentEntries.toImmutableList(),
                    value = lastBalanceValue
                ))
            }
        }
        
        val statesDuration = calculateStressStatesDuration(targetData)
        
        return ChartData(
            chartGranularity = ChartGranularity.DAILY,
            series = seriesList.toImmutableList(),
            highlightEnabled = true,
            goal = null,
            currentValues = createCurrentValuesList(ChartGranularity.DAILY, statesDuration, targetData),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            chartContent = ChartContent.RESOURCES,
            highlightDecorationLines = persistentMapOf(),
            colorIndicator = null,
            recoveryStateMap = recoveryStateMap.toImmutableMap()
        )
    }

    
    private fun createWeeklyAggregatedChartData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        recoveryData: List<RecoveryData>
    ): ChartData {
        val weeklyEntries = mutableListOf<ChartData.Entry>()
        val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
        val weeklyAverages = mutableListOf<Float>()
        
        if (recoveryData.isNotEmpty()) {
            val sortedRecoveryData = recoveryData.sortedBy { it.timestamp }
            
            val dailyRecoveryData = sortedRecoveryData.groupBy { 
                TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate() 
            }
            
            val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
            var currentWeekStart = from.with(firstDayOfWeek)
            if (currentWeekStart.isAfter(from)) {
                currentWeekStart = currentWeekStart.minusWeeks(1)
            }
            
            while (currentWeekStart.isBefore(to) || currentWeekStart.isEqual(to)) {
                val weekEnd = currentWeekStart.plusDays(6)
                val weekData = mutableListOf<RecoveryData>()
                
                for (date in currentWeekStart.daysUntil(weekEnd.plusDays(1))) {
                    if (date.isBefore(from) || date.isAfter(to)) continue
                    dailyRecoveryData[date]?.let { weekData.addAll(it) }
                }
                
                if (weekData.isNotEmpty()) {
                    val balances = weekData.map { it.balance * 100 }
                    
                    val avg = balances.average().toFloat()
                    val min = balances.minOrNull() ?: 0f
                    val max = balances.maxOrNull() ?: 0f
                    
                    weeklyAverages.add(avg)
                    
                    weeklyEntries.add(
                        ChartData.Entry(
                            x = currentWeekStart.toEpochDay(),
                            y = avg
                        )
                    )
                    
                    candlestickEntries.add(
                        ChartData.CandlestickEntry(
                            x = currentWeekStart.toEpochDay(),
                            open = min,
                            close = max,
                            low = min,
                            high = max
                        )
                    )
                }
                
                currentWeekStart = currentWeekStart.plusWeeks(1)
            }
        }
        
        val averageBalance = if (weeklyAverages.isNotEmpty()) {
            weeklyAverages.average().toFloat() / 100
        } else {
            NO_DATA_VALUE
        }
        
        val statesDuration = calculateStressStatesDuration(recoveryData)
        
        val minX = from.toEpochDay().toDouble()
        val maxX = to.toEpochDay().toDouble()
        
        val dataMaxY = if (candlestickEntries.isNotEmpty()) {
            candlestickEntries.maxOfOrNull { it.high.toDouble() } ?: 0.0
        } else if (weeklyAverages.isNotEmpty()) {
            weeklyAverages.maxOfOrNull { it.toDouble() } ?: 0.0
        } else {
            0.0
        }
        val adjustedMaxY = adjustMaxY(dataMaxY)

        val value = createBalanceAnnotatedString(averageBalance)

        val mainSeries = createSeries(
            chartType = ChartType.CANDLESTICK,
            color = context.getColor(BaseR.color.dashboard_widget_hrv),
            axisRange = ChartData.AxisRange(minX, maxX, MIN_Y, adjustedMaxY),
            entries = weeklyEntries.toImmutableList(),
            value = value,
            candlestickEntries = candlestickEntries.toImmutableList()
        )
        
        return ChartData(
            chartGranularity = chartGranularity,
            series = persistentListOf(mainSeries),
            highlightEnabled = true,
            goal = null,
            currentValues = createCurrentValuesList(chartGranularity, statesDuration, recoveryData),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            chartContent = ChartContent.RESOURCES,
            highlightDecorationLines = persistentMapOf(),
            colorIndicator = null,
        )
    }
    
    private fun createMonthlyAggregatedChartData(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        recoveryData: List<RecoveryData>
    ): ChartData {
        val monthlyEntries = mutableListOf<ChartData.Entry>()
        val candlestickEntries = mutableListOf<ChartData.CandlestickEntry>()
        val monthlyAverages = mutableListOf<Float>()
        
        if (recoveryData.isNotEmpty()) {
            val sortedRecoveryData = recoveryData.sortedBy { it.timestamp }
            
            val dailyRecoveryData = sortedRecoveryData.groupBy { 
                TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate() 
            }
            
            var currentMonth = from.withDayOfMonth(1)
            while (currentMonth.isBefore(to) || currentMonth.isEqual(to)) {
                val monthEnd = currentMonth.with(TemporalAdjusters.lastDayOfMonth())
                val monthData = mutableListOf<RecoveryData>()
                
                for (date in currentMonth.daysUntil(monthEnd.plusDays(1))) {
                    if (date.isBefore(from) || date.isAfter(to)) continue
                    dailyRecoveryData[date]?.let { monthData.addAll(it) }
                }
                
                if (monthData.isNotEmpty()) {
                    val balances = monthData.map { it.balance * 100 }
                    
                    val avg = balances.average().toFloat()
                    val min = balances.minOrNull() ?: 0f
                    val max = balances.maxOrNull() ?: 0f
                    
                    monthlyAverages.add(avg)
                    
                    monthlyEntries.add(
                        ChartData.Entry(
                            x = currentMonth.epochMonth.toLong(),
                            y = avg
                        )
                    )
                    
                    candlestickEntries.add(
                        ChartData.CandlestickEntry(
                            x = currentMonth.epochMonth.toLong(),
                            open = min,
                            close = max,
                            low = min,
                            high = max
                        )
                    )
                }
                
                currentMonth = currentMonth.plusMonths(1)
            }
        }
        
        val averageBalance = if (monthlyAverages.isNotEmpty()) {
            monthlyAverages.average().toFloat() / 100
        } else {
            NO_DATA_VALUE
        }
        
        val statesDuration = calculateStressStatesDuration(recoveryData)
        
        val minX = from.epochMonth.toDouble()
        val maxX = to.epochMonth.toDouble()
        
        val dataMaxY = if (candlestickEntries.isNotEmpty()) {
            candlestickEntries.maxOfOrNull { it.high.toDouble() } ?: 0.0
        } else if (monthlyAverages.isNotEmpty()) {
            monthlyAverages.maxOfOrNull { it.toDouble() } ?: 0.0
        } else {
            0.0
        }
        val adjustedMaxY = adjustMaxY(dataMaxY)

        val value = createBalanceAnnotatedString(averageBalance)

        val mainSeries = createSeries(
            chartType = ChartType.CANDLESTICK,
            color = context.getColor(BaseR.color.dashboard_widget_hrv),
            axisRange = ChartData.AxisRange(minX, maxX, MIN_Y, adjustedMaxY),
            entries = monthlyEntries.toImmutableList(),
            value = value,
            candlestickEntries = candlestickEntries.toImmutableList()
        )
        
        return ChartData(
            chartGranularity = chartGranularity,
            series = persistentListOf(mainSeries),
            highlightEnabled = true,
            goal = null,
            currentValues = createCurrentValuesList(chartGranularity, statesDuration, recoveryData),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            chartContent = ChartContent.RESOURCES,
            highlightDecorationLines = persistentMapOf(),
            colorIndicator = null,
        )
    }



    
    private fun createSeries(
        chartType: ChartType,
        color: Int,
        axisRange: ChartData.AxisRange,
        entries: ImmutableList<ChartData.Entry>,
        value: AnnotatedString = AnnotatedString(""),
        candlestickEntries: ImmutableList<ChartData.CandlestickEntry> = persistentListOf()
    ): ChartData.Series {
        return ChartData.Series(
            chartType = chartType,
            color = color,
            axisRange = axisRange,
            entries = entries,
            value = value,
            candlestickEntries = candlestickEntries,
            lineConfig = LineChartConfig(),
            backgroundRegion = null,
            groupStackBarStyle = null,
        )
    }
    
    private fun LocalDate.daysUntil(endExclusive: LocalDate): Sequence<LocalDate> = 
        generateSequence(this) { date -> 
            date.plusDays(1).takeIf { it.isBefore(endExclusive) }
        }
    
    private fun calculateStressStatesDuration(recoveryData: List<RecoveryData>): Map<StressState, Long> {
        if (recoveryData.isEmpty()) {
            return StressState.entries
                .filter { it != StressState.INVALID }
                .associateWith { 0L }
        }
        
        val statesDuration = recoveryData
            .filter { it.stressState != StressState.INVALID }
            .groupBy { it.stressState }
            .mapValues { (_, dataPoints) ->
                dataPoints.size * RECOVERY_DATA_FREQUENCY_SECONDS / 60
            }
        
        return StressState.entries
            .filter { it != StressState.INVALID }
            .associateWith { statesDuration[it] ?: 0L }
    }
    
    fun formatChartHighlightData(
        entry: ChartData.Entry?,
        candlestickEntry: ChartData.CandlestickEntry?
    ): String {
        return when {
            candlestickEntry != null -> {
                val min = candlestickEntry.open.toFloat()
                val max = candlestickEntry.close.toFloat()
                if (min == max) {
                    "${min.roundToInt()}%"
                } else {
                    "${min.roundToInt()}%-${max.roundToInt()}%"
                }
            }
            entry != null -> {
                val percentage = entry.y.toFloat().roundToInt()
                if (percentage == 0) {
                    "$percentage%"
                } else {
                    val level = when {
                        percentage < 20 -> context.getString(R.string.resources_level_low)
                        percentage in 20..50 -> context.getString(R.string.resources_level_moderate)
                        percentage in 51..80 -> context.getString(R.string.resources_level_high)
                        else -> context.getString(R.string.resources_level_very_high)
                    }
                    "$percentage% - $level"
                }
            }
            else -> {
                context.getString(BaseR.string.widget_no_data_title)
            }
        }
    }
    
    private fun createCurrentValuesList(
        chartGranularity: ChartGranularity,
        statesDuration: Map<StressState, Long>,
        recoveryData: List<RecoveryData> = emptyList()
    ): ImmutableList<CurrentValueData> {
        val orderedStates = listOf(
            StressState.ACTIVE,
            StressState.PASSIVE, 
            StressState.STRESSFUL,
            StressState.RELAXING
        )
        
        val stateDaysCount = if (chartGranularity != ChartGranularity.DAILY && recoveryData.isNotEmpty()) {
            recoveryData
                .groupBy { 
                    TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate() 
                }
                .mapValues { (_, dayData) ->
                    dayData.map { it.stressState }.toSet()
                }
                .let { dailyStates ->
                    orderedStates.associateWith { state ->
                        dailyStates.values.count { dayStates -> state in dayStates }.toLong()
                    }
                }
        } else {
            orderedStates.associateWith { 1L }
        }
        
        val stateIcons = mapOf(
            StressState.ACTIVE to R.drawable.ic_resources_active, 
            StressState.PASSIVE to R.drawable.ic_resources_inactive, 
            StressState.STRESSFUL to R.drawable.ic_resources_stressed, 
            StressState.RELAXING to R.drawable.ic_resources_recovering 
        )
        
        val stateColors = if (chartGranularity == ChartGranularity.DAILY) {
            mapOf(
                StressState.ACTIVE to BaseR.color.dashboard_widget_steps,
                StressState.PASSIVE to BaseR.color.dive_set_active_mode_button_border_color,
                StressState.STRESSFUL to BaseR.color.second_indicator_color,
                StressState.RELAXING to BaseR.color.dashboard_widget_hrv
            )
        } else {
            mapOf(
                StressState.ACTIVE to BaseR.color.text_sub_title,
                StressState.PASSIVE to BaseR.color.text_sub_title,
                StressState.STRESSFUL to BaseR.color.text_sub_title,
                StressState.RELAXING to BaseR.color.text_sub_title
            )
        }
        
        val stateNames = if (chartGranularity == ChartGranularity.DAILY) {
            mapOf(
                StressState.ACTIVE to context.getString(R.string.resources_state_active),
                StressState.PASSIVE to context.getString(R.string.resources_state_inactive),
                StressState.STRESSFUL to context.getString(R.string.resources_state_stressed),
                StressState.RELAXING to context.getString(R.string.resources_state_recovering)
            )
        } else {
            mapOf(
                StressState.ACTIVE to context.getString(R.string.resources_state_active_avg),
                StressState.PASSIVE to context.getString(R.string.resources_state_inactive_avg),
                StressState.STRESSFUL to context.getString(R.string.resources_state_stressed_avg),
                StressState.RELAXING to context.getString(R.string.resources_state_recovering_avg)
            )
        }
        
        return orderedStates.map { state ->
            val minutes = statesDuration[state] ?: 0L
            val stateDays = stateDaysCount[state] ?: 1L
            
            val adjustedMinutes = if (chartGranularity == ChartGranularity.DAILY) {
                minutes
            } else {
                if (stateDays > 0) (minutes.toDouble() / stateDays).roundToInt().toLong() else 0L
            }
            
            val duration = adjustedMinutes.toDuration(DurationUnit.MINUTES)
            val hours = duration.inWholeHours
            val remainingMinutes = duration.inWholeMinutes - (hours * 60)
            
            val formattedDuration = buildAnnotatedString {
                when {
                    hours > 0 -> {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold, fontSize = 18.sp)) {
                            append(hours.toString())
                        }
                        withStyle(SpanStyle(fontSize = 12.sp)) {
                            append(" ")
                            append(context.getString(CR.string.hour))
                        }
                        if (remainingMinutes > 0) {
                            withStyle(SpanStyle(fontSize = 12.sp)) {
                                append(" ")
                            }
                            withStyle(SpanStyle(fontWeight = FontWeight.Bold, fontSize = 18.sp)) {
                                append(remainingMinutes.toString())
                            }
                            withStyle(SpanStyle(fontSize = 12.sp)) {
                                append(" ")
                                append(context.getString(CR.string.minute))
                            }
                        }
                    }
                    remainingMinutes == 0L -> {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold, fontSize = 18.sp)) {
                            append("0")
                        }
                        withStyle(SpanStyle(fontSize = 12.sp)) {
                            append(" ")
                            append(context.getString(CR.string.hour))
                        }
                    }
                    else -> {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold, fontSize = 18.sp)) {
                            append(remainingMinutes.toString())
                        }
                        withStyle(SpanStyle(fontSize = 12.sp)) {
                            append(" ")
                            append(context.getString(CR.string.minute))
                        }
                    }
                }
            }
            
            CurrentValueData(
                value = formattedDuration,
                explanation =  stateNames[state] ?: "",
                icon = stateIcons[state] ?: 0,
                iconColor = stateColors[state] ?: 0
            )
        }.toImmutableList()
    }

    fun formatDailyChartHighlightDateTime(x : Long): String {
        return buildString {
            val minutesSinceEpoch = x.minutes
            ZonedDateTime
                .ofInstant(
                    Instant.ofEpochSecond(minutesSinceEpoch.inWholeSeconds),
                    ZoneId.systemDefault()
                )
                .toLocalDate()
                .let(chartHighlightDateFormatter::formatAbsoluteDate)
                .let(::append)

            append(", ")

            val millisSinceEpoch = minutesSinceEpoch.inWholeMilliseconds
            TextFormatter.formatTime(context, millisSinceEpoch)
                .let(::append)

            append('-')

            TextFormatter.formatTime(context, millisSinceEpoch + 30.minutes.inWholeMilliseconds)
                .let(::append)
        }
    }

    private fun calculateFinalBalance(
        date: LocalDate,
        currentBalance: Float,
        targetData: List<RecoveryData>
    ): Float {
        return if (date == LocalDate.now() && currentBalance != NO_DATA_VALUE) {
            currentBalance
        } else {
            val newestRecoveryData = targetData.lastOrNull()
            if (newestRecoveryData != null &&
                TimeUtils.epochToLocalZonedDateTime(newestRecoveryData.timestamp).toLocalDate() == date) {
                newestRecoveryData.balance
            } else {
                0f
            }
        }
    }

    private fun createBalanceAnnotatedString(balance: Float): AnnotatedString {
        return buildAnnotatedString {
            val percentage = if (balance == NO_DATA_VALUE) {
                0
            } else {
                (balance * 100).roundToInt()
            }
            
            val level = when {
                percentage == 0 -> null
                percentage < 20 -> context.getString(R.string.resources_level_low)
                percentage in 20..50 -> context.getString(R.string.resources_level_moderate)
                percentage in 51..80 -> context.getString(R.string.resources_level_high)
                else -> context.getString(R.string.resources_level_very_high)
            }
            
            withStyle(SpanStyle(fontSize = 24.sp, fontWeight = FontWeight.Bold)) {
                append("$percentage")
            }

            withStyle(SpanStyle(fontSize = 12.sp, fontWeight = FontWeight.Bold)) {
                append("%")
            }

            if (level != null) {
                withStyle(SpanStyle(fontSize = 24.sp, fontWeight = FontWeight.Bold)) {
                    append(" - ")
                    append(level)
                }
            }
        }
    }

    
    fun getDailyValueTypeResourceId(stressState: StressState?): Int {
        return when (stressState) {
            StressState.ACTIVE -> R.string.resources_state_active
            StressState.PASSIVE -> R.string.resources_state_inactive
            StressState.STRESSFUL -> R.string.resources_state_stressed
            StressState.RELAXING -> R.string.resources_state_recovering
            StressState.INVALID, null -> R.string.chart_value_active
        }
    }
} 
