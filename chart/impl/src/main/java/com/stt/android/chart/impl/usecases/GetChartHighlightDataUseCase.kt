package com.stt.android.chart.impl.usecases

import com.stt.android.chart.api.model.ChartComparison
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.data.CaloriesDataLoader
import com.stt.android.chart.impl.data.AscentDataLoader
import com.stt.android.chart.impl.data.CommuteDataLoader
import com.stt.android.chart.impl.data.DurationDataLoader
import com.stt.android.chart.impl.data.HeartRateDataLoader
import com.stt.android.chart.impl.data.MinimumHeartRateDataLoader
import com.stt.android.chart.impl.data.ResourcesDataLoader
import com.stt.android.chart.impl.data.RestingHeartRateDataLoader
import com.stt.android.chart.impl.data.SleepDataLoader
import com.stt.android.chart.impl.data.SleepMinimumHeartRateDataLoader
import com.stt.android.chart.impl.data.StepDataLoader
import com.stt.android.chart.impl.data.HrvDataLoader
import com.stt.android.chart.impl.data.TSSDataLoader
import com.stt.android.chart.impl.data.Vo2MaxDataLoader
import com.stt.android.chart.impl.model.highlightTitleRes
import com.stt.android.chart.impl.model.titleRes
import com.stt.android.chart.impl.screen.ChartComparisonViewData
import com.stt.android.chart.impl.screen.ChartHighlightViewData
import com.stt.android.chart.impl.screen.ChartViewData
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

internal class GetChartHighlightDataUseCase @Inject constructor(
    private val stepDataLoader: StepDataLoader,
    private val sleepDataLoader: SleepDataLoader,
    private val minimumHeartRateDataLoader: MinimumHeartRateDataLoader,
    private val sleepMinimumHeartRateDataLoader: SleepMinimumHeartRateDataLoader,
    private val heartRateDataLoader: HeartRateDataLoader,
    private val restingHeartRateDataLoader: RestingHeartRateDataLoader,
    private val caloriesDataLoader: CaloriesDataLoader,
    private val formatDateTimeUseCase: FormatChartHighlightDateTimeUseCase,
    private val resourcesDataLoader: ResourcesDataLoader,
    private val ascentDataLoader: AscentDataLoader,
    private val durationDataLoader: DurationDataLoader,
    private val hrvDataLoader: HrvDataLoader,
    private val vo2MaxDataLoader: Vo2MaxDataLoader,
    private val commuteDataLoader: CommuteDataLoader,
    private val tssDataLoader: TSSDataLoader,
) {
    suspend operator fun invoke(
        chartContent: ChartContent,
        chartComparison: ChartComparison,
        viewData: ChartViewData.Loaded,
        entryX: Long,
        selectedHeartRateStatId:String?
    ): ChartHighlightViewData = viewData.chartData[viewData.currentChartPage]
        ?.firstOrNull()
        ?.let { chartData ->
            when (chartContent) {
                ChartContent.CALORIES,
                ChartContent.STEPS -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null

                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }
                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    val comparisonDateTime = formatDateTimeUseCase.formatComparisonDateTime(
                        chartGranularity = chartData.chartGranularity,
                        chartComparison = chartComparison,
                        x = entryX,
                    )
                    val (leftColorRes, rightColorRes) = getChartComparisonColors(viewData.chartComparison)
                    
                    ChartHighlightViewData.Highlight(
                        valueType = chartData.chartGranularity.highlightTitleRes,
                        primaryValue = ChartHighlightViewData.Value(
                            value = if (chartContent == ChartContent.CALORIES) caloriesDataLoader.formatHighlightData(
                                primaryEntry?.y?.toInt()
                            ) else
                                stepDataLoader.formatHighlightData(primaryEntry?.y?.toInt()),
                            dateTime = primaryDateTime,
                            colorRes = leftColorRes,
                        ),
                        comparisonValue = if (chartComparison == ChartComparison.LAST_PERIOD) {
                            ChartHighlightViewData.Value(
                                value = if (chartContent == ChartContent.CALORIES) caloriesDataLoader.formatHighlightData(
                                    comparisonEntry?.y?.toInt()
                                ) else
                                    stepDataLoader.formatHighlightData(comparisonEntry?.y?.toInt()),
                                dateTime = comparisonDateTime,
                                colorRes = rightColorRes,
                            )
                        } else null
                    )
                }

                ChartContent.SLEEP -> {
                    if (chartData.chartGranularity == ChartGranularity.DAILY) {
                        val sleepStageEntry = chartData.series
                            .firstOrNull()
                            ?.sleepStageEntries
                            ?.firstOrNull { it.xStart == entryX }
                            ?: return@let null
                        ChartHighlightViewData.Highlight(
                            valueType = sleepStageEntry.stage.titleRes,
                            primaryValue = ChartHighlightViewData.Value(
                                value = sleepDataLoader.formatDailyHighlightData(
                                    sleepStageEntry.xStart,
                                    sleepStageEntry.xEnd,
                                ),
                                dateTime = sleepDataLoader.formatDailyHighlightDateTime(
                                    sleepStageEntry.xStart,
                                    sleepStageEntry.xEnd,
                                )
                            ),
                            comparisonValue = null,
                        )
                    } else {
                        val entry = chartData.series
                            .firstOrNull()
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                        val secondEntry = chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                        if (entry == null && secondEntry == null) {
                            return@let null
                        }
                        val total = (entry?.y?.toFloat() ?: 0f) + (secondEntry?.y?.toFloat() ?: 0f)
                        ChartHighlightViewData.Highlight(
                            valueType = chartData.chartGranularity.highlightTitleRes,
                            primaryValue = ChartHighlightViewData.Value(
                                value = sleepDataLoader.formatHighlightData(total),
                                dateTime = formatDateTimeUseCase.formatDateTime(
                                    chartGranularity = chartData.chartGranularity,
                                    x = entryX,
                                ),
                            ),
                            comparisonValue = null,
                        )
                    }
                }

                ChartContent.DURATION -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null

                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }
                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    val comparisonDateTime = formatDateTimeUseCase.formatComparisonDateTime(
                        chartGranularity = chartData.chartGranularity,
                        chartComparison = chartComparison,
                        x = entryX,
                    )

                    val (leftColorRes, rightColorRes) = getChartComparisonColors(viewData.chartComparison)

                    ChartHighlightViewData.Highlight(
                        valueType = chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity),
                        primaryValue = ChartHighlightViewData.Value(
                            value = durationDataLoader.formatHighlightData(primaryEntry?.y?.toDouble()),
                            dateTime = primaryDateTime,
                            colorRes = leftColorRes,
                        ),
                        comparisonValue = if (chartComparison == ChartComparison.LAST_PERIOD) {
                            ChartHighlightViewData.Value(
                                value = durationDataLoader.formatHighlightData(comparisonEntry?.y?.toDouble()),
                                dateTime = comparisonDateTime,
                                colorRes = rightColorRes,
                            )
                        } else null
                    )
                }
                ChartContent.ASCENT -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null

                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }
                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    val comparisonDateTime = formatDateTimeUseCase.formatComparisonDateTime(
                        chartGranularity = chartData.chartGranularity,
                        chartComparison = chartComparison,
                        x = entryX,
                    )

                    val (leftColorRes, rightColorRes) = getChartComparisonColors(viewData.chartComparison)

                    ChartHighlightViewData.Highlight(
                        valueType = chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity),
                        primaryValue = ChartHighlightViewData.Value(
                            value = ascentDataLoader.formatHighlightData(primaryEntry?.y?.toDouble()),
                            dateTime = primaryDateTime,
                            colorRes = leftColorRes,
                        ),
                        comparisonValue = if (chartComparison == ChartComparison.LAST_PERIOD) {
                            ChartHighlightViewData.Value(
                                value = ascentDataLoader.formatHighlightData(comparisonEntry?.y?.toDouble()),
                                dateTime = comparisonDateTime,
                                colorRes = rightColorRes,
                            )
                        } else null
                    )
                }
                ChartContent.TSS,
                    -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null
                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }

                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    val comparisonDateTime = formatDateTimeUseCase.formatComparisonDateTime(
                        chartGranularity = chartData.chartGranularity,
                        chartComparison = chartComparison,
                        x = entryX,
                    )

                    ChartHighlightViewData.Highlight(
                        valueType = chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity),
                        primaryValue = ChartHighlightViewData.Value(
                            value = tssDataLoader.formatHighlightData(primaryEntry?.y),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = comparisonEntry?.y
                            ?.let { comparisonY ->
                                ChartHighlightViewData.Value(
                                    value = tssDataLoader.formatHighlightData(comparisonY),
                                    dateTime = comparisonDateTime,
                                )
                            },
                    )
                }
                ChartContent.MINIMUM_HEART_RATE -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null

                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }
                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    ChartHighlightViewData.Highlight(
                        valueType = chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity),
                        primaryValue = ChartHighlightViewData.Value(
                            value = minimumHeartRateDataLoader.formatHighlightData(primaryEntry?.y?.toFloat()),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = null, // TODO

                    )
                }

                ChartContent.SLEEPING_MINIMUM_HEART_RATE -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null

                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }
                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    ChartHighlightViewData.Highlight(
                        valueType = chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity),
                        primaryValue = ChartHighlightViewData.Value(
                            value = sleepMinimumHeartRateDataLoader.formatHighlightData(primaryEntry?.y?.toFloat()),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = null, // TODO
                    )
                }

                ChartContent.HEART_RATE -> {
                    val primaryEntry = chartData.series
                        .asSequence()
                        .mapNotNull { series ->
                            series.entries.firstOrNull { it.x == entryX }
                        }
                        .firstOrNull()

                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null

                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }
                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val (value, valueType, dateTimeStr) = heartRateDataLoader.formatHeartRateHighlightData(
                        chartData = chartData,
                        selectedHeartRateStatId = selectedHeartRateStatId,
                        entryX = entryX,
                    )

                    ChartHighlightViewData.Highlight(
                        valueType = valueType,
                        primaryValue = ChartHighlightViewData.Value(
                            value = value,
                            dateTime = dateTimeStr,
                        ),
                        comparisonValue = null, // TODO
                    )
                }

                ChartContent.RESTING_HEART_RATE -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null

                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }
                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    ChartHighlightViewData.Highlight(
                        valueType = chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity),
                        primaryValue = ChartHighlightViewData.Value(
                            value = restingHeartRateDataLoader.formatHighlightData(primaryEntry?.y?.toFloat()),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = null, // TODO
                    )
                }
                ChartContent.COMMUTE -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.entries
                        ?.firstOrNull { it.x == entryX }
                    val comparisonEntry = when (chartComparison) {
                        ChartComparison.NONE,
                        ChartComparison.RIGHT_AXIS -> null
                        ChartComparison.LAST_PERIOD -> chartData.series
                            .getOrNull(1)
                            ?.entries
                            ?.firstOrNull { it.x == entryX }
                    }

                    if (primaryEntry == null && comparisonEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )
                    val comparisonDateTime = formatDateTimeUseCase.formatComparisonDateTime(
                        chartGranularity = chartData.chartGranularity,
                        chartComparison = chartComparison,
                        x = entryX,
                    )

                    ChartHighlightViewData.Highlight(
                        valueType = chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity),
                        primaryValue = ChartHighlightViewData.Value(
                            value = commuteDataLoader.formatHighlightData(primaryEntry?.y?.toDouble()),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = comparisonEntry?.y
                            ?.toDouble()
                            ?.let { comparisonY ->
                                ChartHighlightViewData.Value(
                                    value = commuteDataLoader.formatHighlightData(comparisonY),
                                    dateTime = comparisonDateTime,
                                )
                            },
                    )
                }

                ChartContent.RESOURCES -> {
                    val primaryEntry = chartData.series
                        .flatMap { it.entries }
                        .firstOrNull { it.x == entryX }

                    if (primaryEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = when (chartData.chartGranularity) {
                        ChartGranularity.DAILY -> {
                            resourcesDataLoader.formatDailyChartHighlightDateTime(entryX)
                        }
                        else -> {
                            formatDateTimeUseCase.formatDateTime(
                                chartGranularity = chartData.chartGranularity,
                                x = entryX,
                            )
                        }
                    }
                    
                    val candlestickEntry = chartData.series
                        .firstOrNull()
                        ?.candlestickEntries
                        ?.firstOrNull { it.x == entryX }
                    
                    val valueType = when (chartData.chartGranularity) {
                        ChartGranularity.DAILY -> {
                            val stressState = chartData.recoveryStateMap[entryX]
                            resourcesDataLoader.getDailyValueTypeResourceId(stressState)
                        }
                        else -> {
                            chartContent.highlightTitleRes(chartGranularity = chartData.chartGranularity)
                        }
                    }
                    
                    ChartHighlightViewData.Highlight(
                        valueType = valueType,
                        primaryValue = ChartHighlightViewData.Value(
                            value = resourcesDataLoader.formatChartHighlightData(primaryEntry, candlestickEntry),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = null, // TODO
                    )
                }
                ChartContent.HRV -> {
                    val primaryEntry = chartData.series
                        .firstOrNull()
                        ?.gradientEntries
                        ?.firstOrNull { it.x == entryX }

                    if (primaryEntry == null) {
                        return@let null
                    }

                    val primaryDateTime = formatDateTimeUseCase.formatDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )

                    val valueType = hrvDataLoader.getValueType(
                        chartGranularity = chartData.chartGranularity,
                        y = primaryEntry.y.toFloat()
                    )

                    ChartHighlightViewData.Highlight(
                        valueType = valueType,
                        primaryValue = ChartHighlightViewData.Value(
                            value = hrvDataLoader.formatHighlightData(primaryEntry.y.toFloat()),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = null,
                    )
                }
                
                ChartContent.VO2MAX -> {
                    val yValue = chartData.seriesXYMap[entryX]?.toFloat()
                        ?: return@let null

                    val primaryDateTime = vo2MaxDataLoader.formatHighlightDateTime(
                        chartGranularity = chartData.chartGranularity,
                        x = entryX,
                    )

                    val valueType = vo2MaxDataLoader.getValueTypeForVo2Max(
                        chartGranularity = chartData.chartGranularity,
                        y = yValue)

                    ChartHighlightViewData.Highlight(
                        valueType = valueType,
                        primaryValue = ChartHighlightViewData.Value(
                            value = vo2MaxDataLoader.formatHighlightData(yValue),
                            dateTime = primaryDateTime,
                        ),
                        comparisonValue = null,
                    )
                }
            }
        }
        ?: ChartHighlightViewData.None
    private fun getChartComparisonColors(chartComparison: ChartComparisonViewData): Pair<Int?, Int?> {
        return when (chartComparison) {
            is ChartComparisonViewData.LastPeriod -> chartComparison.leftColorRes to chartComparison.rightColorRes
            is ChartComparisonViewData.RightChart -> chartComparison.leftColorRes to chartComparison.rightColorRes
            else -> null to null
        }
    }
}

